# CI/CD Pipeline Implementation Plan

## Overview
This document outlines the implementation plan for creating a CI/CD pipeline using GitHub Actions for the sales forecasting project.

## Implementation Steps

### Step 1: Create CI Workflow
- Create `.github/workflows/ci.yml`
- Implement code linting
- Add unit testing
- Add integration testing

### Step 2: Create CD Workflow
- Create `.github/workflows/cd.yml`
- Implement deployment to staging
- Add deployment to production
- Add approval gates

### Step 3: Add Code Quality Checks
- Implement code formatting checks
- Add security scanning
- Add dependency vulnerability scanning

### Step 4: Configure Automated Testing
- Set up test environments
- Configure test data
- Add test reporting

## Detailed Task Breakdown

### Task 1: Create CI Workflow
**Estimated Time**: 2 hours

Subtasks:
1. Create `.github/workflows/ci.yml`
2. Implement Python code linting with flake8
3. Add unit testing with pytest
4. Add integration testing
5. Test workflow execution

### Task 2: Create CD Workflow
**Estimated Time**: 3 hours

Subtasks:
1. Create `.github/workflows/cd.yml`
2. Implement staging deployment
3. Add production deployment
4. Configure approval gates
5. Test deployment workflow

### Task 3: Add Code Quality Checks
**Estimated Time**: 2 hours

Subtasks:
1. Add code formatting checks with black
2. Implement security scanning with bandit
3. Add dependency vulnerability scanning
4. Configure quality gates
5. Test quality checks

### Task 4: Configure Automated Testing
**Estimated Time**: 2 hours

Subtasks:
1. Set up test environments
2. Configure test data management
3. Add test reporting
4. Configure test notifications
5. Test automated testing

## Implementation Details

### CI Workflow

#### File: .github/workflows/ci.yml
```yaml
name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f ui/requirements.txt ]; then pip install -r ui/requirements.txt; fi
    
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  test:
    runs-on: ubuntu-latest
    needs: lint
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10']
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f ui/requirements.txt ]; then pip install -r ui/requirements.txt; fi
    
    - name: Run unit tests
      run: |
        pytest tests/ -v --cov=./ --cov-report=xml
    
    - name: Run doctests
      run: |
        python -m doctest -v include/ml_models/*.py
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  integration-test:
    runs-on: ubuntu-latest
    needs: test
    services:
      postgres:
        image: postgres:12
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        if [ -f ui/requirements.txt ]; then pip install -r ui/requirements.txt; fi
    
    - name: Run integration tests
      run: |
        pytest tests/integration/ -v
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

  security-scan:
    runs-on: ubuntu-latest
    needs: test
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install bandit
      run: |
        pip install bandit
    
    - name: Run security scan
      run: |
        bandit -r . -f json -o bandit-report.json || true
    
    - name: Upload security report
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: bandit-report.json

  dependency-check:
    runs-on: ubuntu-latest
    needs: test
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install safety
      run: |
        pip install safety
    
    - name: Check dependencies
      run: |
        safety check -r requirements.txt || true
        safety check -r ui/requirements.txt || true
```

### CD Workflow

#### File: .github/workflows/cd.yml
```yaml
name: CD Pipeline

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: ${{ secrets.DOCKER_USERNAME }}/sales-forecasting:latest

  deploy-staging:
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: staging
    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment"
        # Add deployment commands here
        # Example: ssh to staging server and pull latest image
        # ssh ${{ secrets.STAGING_SSH }} "cd /app && docker-compose pull && docker-compose up -d"
    
    - name: Run staging tests
      run: |
        echo "Running staging tests"
        # Add staging tests here

  deploy-production:
    runs-on: ubuntu-latest
    needs: deploy-staging
    environment: production
    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment"
        # Add deployment commands here
        # Example: ssh to production server and pull latest image
        # ssh ${{ secrets.PRODUCTION_SSH }} "cd /app && docker-compose pull && docker-compose up -d"
    
    - name: Run production tests
      run: |
        echo "Running production tests"
        # Add production tests here
    
    - name: Send deployment notification
      run: |
        echo "Deployment completed successfully"
        # Add notification logic here (Slack, email, etc.)
```

### Enhanced CI with Docker Testing

#### File: .github/workflows/ci-docker.yml
```yaml
name: CI with Docker

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  docker-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Build Docker images
      run: |
        docker-compose -f docker-compose.override.yml build
    
    - name: Start services
      run: |
        docker-compose -f docker-compose.override.yml up -d
    
    - name: Wait for services
      run: |
        sleep 30
        docker-compose -f docker-compose.override.yml ps
    
    - name: Test service health
      run: |
        # Test FastAPI health endpoint
        curl -f http://localhost:8000/health || exit 1
        
        # Test Streamlit health (if available)
        # curl -f http://localhost:8501/healthz || exit 1
    
    - name: Run integration tests
      run: |
        # Add integration tests that run against the Docker services
        echo "Running integration tests against Docker services"
    
    - name: Stop services
      if: always()
      run: |
        docker-compose -f docker-compose.override.yml down
```

### Code Quality Checks

#### File: .github/workflows/code-quality.yml
```yaml
name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  formatting:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install black
      run: |
        pip install black
    
    - name: Check code formatting
      run: |
        black --check .

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install bandit
      run: |
        pip install bandit
    
    - name: Run security scan
      run: |
        bandit -r . -lll

  dependencies:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'
    
    - name: Install pip-audit
      run: |
        pip install pip-audit
    
    - name: Audit dependencies
      run: |
        pip-audit -r requirements.txt || true
        pip-audit -r ui/requirements.txt || true
```

### Automated Testing Configuration

#### pytest configuration in pyproject.toml
```toml
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --cov=./ --cov-report=html --cov-report=term-missing"
testpaths = [
    "tests",
]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*", "*Test"]
python_functions = ["test_*"]

[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/site-packages/*",
    "*/.eggs/*",
    "*/setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
]
```

#### Test environment configuration
```python
# tests/conftest.py
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import tempfile
import os

@pytest.fixture
def sample_sales_data():
    """Create sample sales data for testing"""
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    data = {
        'date': dates,
        'store_id': ['store_001'] * 100,
        'product_id': ['product_001'] * 100,
        'sales': np.random.normal(1000, 200, 100),
        'promotions': np.random.binomial(1, 0.2, 100),
        'temperature': np.random.normal(25, 5, 100)
    }
    return pd.DataFrame(data)

@pytest.fixture
def temp_directory():
    """Create temporary directory for tests"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir

@pytest.fixture
def mock_mlflow():
    """Mock MLflow for testing"""
    with patch('mlflow') as mock_mlflow:
        yield mock_mlflow
```

## Testing Plan

### Unit Tests
- Test GitHub Actions workflow syntax
- Test individual job execution
- Test environment variables and secrets
- Test artifact handling

### Integration Tests
- Test end-to-end workflow execution
- Test Docker image building
- Test service deployment
- Test notification systems

### Manual Testing
- Trigger workflows manually
- Verify workflow execution
- Check test results and reports
- Validate deployments

## Success Criteria

1. CI workflow executes on push and pull requests
2. Code linting and testing passes
3. Security and dependency checks run
4. CD workflow deploys to staging
5. Production deployment requires approval
6. All tests pass before deployment
7. Notifications are sent on deployment
8. Code coverage is reported

## Dependencies

- GitHub repository
- GitHub Actions enabled
- Docker Hub account (for image pushing)
- Test environment access
- Notification service configuration

## Timeline Estimate

| Task | Estimated Time |
|------|----------------|
| Create CI Workflow | 2 hours |
| Create CD Workflow | 3 hours |
| Add Code Quality Checks | 2 hours |
| Configure Automated Testing | 2 hours |
| Testing | 2 hours |
| **Total** | **11 hours** |