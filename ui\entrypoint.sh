#!/bin/bash

# Install system dependencies for LightGBM
apt-get update && apt-get install -y libgomp1

# Install dependencies
pip install streamlit==1.32.2 pandas==2.2.0 numpy==1.26.3 plotly==5.19.0 mlflow==2.9.2 scikit-learn==1.4.0 xgboost==2.0.3 lightgbm==4.3.0 joblib==1.3.2 boto3==1.34.25 python-dateutil==2.8.2 dill==0.3.7

# Run Streamlit app
streamlit run inference_app.py --server.address 0.0.0.0 --server.port 8501