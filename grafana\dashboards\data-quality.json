{"dashboard": {"id": null, "title": "Data Quality", "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "5s", "panels": [{"id": 1, "type": "gauge", "title": "Data Quality Score", "gridPos": {"x": 0, "y": 0, "w": 6, "h": 6}, "targets": [{"expr": "data_quality_score", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 2, "type": "graph", "title": "Missing Values Over Time", "gridPos": {"x": 6, "y": 0, "w": 12, "h": 6}, "targets": [{"expr": "increase(missing_values_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 3, "type": "stat", "title": "Outliers Detected", "gridPos": {"x": 0, "y": 6, "w": 6, "h": 4}, "targets": [{"expr": "increase(outliers_detected_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 4, "type": "graph", "title": "Data Validation Failures", "gridPos": {"x": 6, "y": 6, "w": 12, "h": 4}, "targets": [{"expr": "increase(data_validation_failures_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}]}}