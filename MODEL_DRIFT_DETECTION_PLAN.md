# Model Drift Detection Implementation Plan

## Overview
This document outlines the implementation plan for adding model drift detection capabilities to the sales forecasting project.

## Implementation Steps

### Step 1: Create Drift Detection Module
- Create `include/ml_models/drift_detection.py`
- Implement statistical drift detection methods
- Add feature drift detection
- Add prediction drift detection

### Step 2: Integrate with Retraining Pipeline
- Add drift detection to retraining DAG
- Implement drift-based triggering
- Add drift reporting

### Step 3: Add Monitoring and Alerting
- Add drift metrics to Prometheus
- Create Grafana dashboard for drift visualization
- Implement alerting for significant drift

## Detailed Task Breakdown

### Task 1: Create Drift Detection Module
**Estimated Time**: 3 hours

Subtasks:
1. Create `include/ml_models/drift_detection.py`
2. Implement Kolmogorov-Smirnov test for feature drift
3. Implement PSI (Population Stability Index) calculation
4. Add prediction drift detection
5. Add concept drift detection

### Task 2: Integrate with Retraining Pipeline
**Estimated Time**: 2 hours

Subtasks:
1. Add drift detection to retraining DAG
2. Implement drift-based retraining triggers
3. Add drift reporting to MLflow
4. Test integration with existing pipeline

### Task 3: Add Monitoring and Alerting
**Estimated Time**: 2 hours

Subtasks:
1. Add drift metrics to Prometheus
2. Create Grafana dashboard for drift
3. Implement alerting rules
4. Test monitoring and alerting

## Implementation Details

### Drift Detection Module

#### File: include/ml_models/drift_detection.py
```python
"""
Model Drift Detection Module
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class DriftDetector:
    """Class for detecting various types of drift in ML models"""
    
    def __init__(self, reference_data: pd.DataFrame):
        """
        Initialize drift detector with reference data
        
        Args:
            reference_data: Reference dataset for comparison
        """
        self.reference_data = reference_data
        self.reference_stats = self._calculate_reference_stats()
    
    def _calculate_reference_stats(self) -> Dict:
        """Calculate statistics for reference data"""
        stats_dict = {}
        for column in self.reference_data.select_dtypes(include=[np.number]).columns:
            stats_dict[column] = {
                'mean': self.reference_data[column].mean(),
                'std': self.reference_data[column].std(),
                'min': self.reference_data[column].min(),
                'max': self.reference_data[column].max(),
                'quantiles': self.reference_data[column].quantile([0.25, 0.5, 0.75]).to_dict()
            }
        return stats_dict
    
    def detect_feature_drift(self, current_data: pd.DataFrame, 
                           threshold: float = 0.05) -> Dict:
        """
        Detect feature drift using Kolmogorov-Smirnov test
        
        Args:
            current_data: Current dataset to compare
            threshold: P-value threshold for drift detection
            
        Returns:
            Dictionary with drift detection results
        """
        drift_results = {}
        
        # Get numeric columns
        numeric_columns = current_data.select_dtypes(include=[np.number]).columns
        reference_numeric = self.reference_data[numeric_columns]
        current_numeric = current_data[numeric_columns]
        
        for column in numeric_columns:
            if column in reference_numeric.columns:
                # Kolmogorov-Smirnov test
                try:
                    ks_statistic, p_value = stats.ks_2samp(
                        reference_numeric[column].dropna(),
                        current_numeric[column].dropna()
                    )
                    
                    drift_results[column] = {
                        'ks_statistic': float(ks_statistic),
                        'p_value': float(p_value),
                        'drift_detected': p_value < threshold,
                        'severity': self._calculate_drift_severity(p_value)
                    }
                except Exception as e:
                    logger.warning(f"Error calculating KS test for {column}: {e}")
                    drift_results[column] = {
                        'ks_statistic': 0.0,
                        'p_value': 1.0,
                        'drift_detected': False,
                        'severity': 'none',
                        'error': str(e)
                    }
        
        return drift_results
    
    def calculate_psi(self, current_data: pd.DataFrame, 
                     buckets: int = 10) -> Dict:
        """
        Calculate Population Stability Index for features
        
        Args:
            current_data: Current dataset to compare
            buckets: Number of buckets for PSI calculation
            
        Returns:
            Dictionary with PSI results
        """
        psi_results = {}
        
        # Get numeric columns
        numeric_columns = current_data.select_dtypes(include=[np.number]).columns
        reference_numeric = self.reference_data[numeric_columns]
        current_numeric = current_data[numeric_columns]
        
        for column in numeric_columns:
            if column in reference_numeric.columns:
                try:
                    # Create bins from reference data
                    bins = np.linspace(
                        min(reference_numeric[column].min(), current_numeric[column].min()),
                        max(reference_numeric[column].max(), current_numeric[column].max()),
                        buckets + 1
                    )
                    
                    # Calculate distributions
                    reference_counts, _ = np.histogram(reference_numeric[column].dropna(), bins=bins)
                    current_counts, _ = np.histogram(current_numeric[column].dropna(), bins=bins)
                    
                    # Normalize to proportions
                    reference_prop = reference_counts / len(reference_numeric[column].dropna())
                    current_prop = current_counts / len(current_numeric[column].dropna())
                    
                    # Avoid division by zero
                    reference_prop = np.where(reference_prop == 0, 0.0001, reference_prop)
                    current_prop = np.where(current_prop == 0, 0.0001, current_prop)
                    
                    # Calculate PSI
                    psi = np.sum((current_prop - reference_prop) * np.log(current_prop / reference_prop))
                    
                    psi_results[column] = {
                        'psi': float(psi),
                        'drift_level': self._interpret_psi(psi),
                        'reference_distribution': reference_prop.tolist(),
                        'current_distribution': current_prop.tolist()
                    }
                except Exception as e:
                    logger.warning(f"Error calculating PSI for {column}: {e}")
                    psi_results[column] = {
                        'psi': 0.0,
                        'drift_level': 'none',
                        'error': str(e)
                    }
        
        return psi_results
    
    def detect_prediction_drift(self, reference_predictions: np.array,
                              current_predictions: np.array,
                              threshold: float = 0.05) -> Dict:
        """
        Detect drift in model predictions
        
        Args:
            reference_predictions: Reference predictions
            current_predictions: Current predictions
            threshold: P-value threshold for drift detection
            
        Returns:
            Dictionary with prediction drift results
        """
        try:
            # Kolmogorov-Smirnov test on predictions
            ks_statistic, p_value = stats.ks_2samp(
                reference_predictions,
                current_predictions
            )
            
            return {
                'ks_statistic': float(ks_statistic),
                'p_value': float(p_value),
                'drift_detected': p_value < threshold,
                'severity': self._calculate_drift_severity(p_value)
            }
        except Exception as e:
            logger.error(f"Error detecting prediction drift: {e}")
            return {
                'ks_statistic': 0.0,
                'p_value': 1.0,
                'drift_detected': False,
                'severity': 'none',
                'error': str(e)
            }
    
    def detect_concept_drift(self, reference_targets: np.array,
                           current_targets: np.array,
                           threshold: float = 0.05) -> Dict:
        """
        Detect concept drift (target distribution changes)
        
        Args:
            reference_targets: Reference target values
            current_targets: Current target values
            threshold: P-value threshold for drift detection
            
        Returns:
            Dictionary with concept drift results
        """
        try:
            # Kolmogorov-Smirnov test on target values
            ks_statistic, p_value = stats.ks_2samp(
                reference_targets,
                current_targets
            )
            
            return {
                'ks_statistic': float(ks_statistic),
                'p_value': float(p_value),
                'drift_detected': p_value < threshold,
                'severity': self._calculate_drift_severity(p_value)
            }
        except Exception as e:
            logger.error(f"Error detecting concept drift: {e}")
            return {
                'ks_statistic': 0.0,
                'p_value': 1.0,
                'drift_detected': False,
                'severity': 'none',
                'error': str(e)
            }
    
    def _calculate_drift_severity(self, p_value: float) -> str:
        """Calculate drift severity based on p-value"""
        if p_value < 0.001:
            return 'severe'
        elif p_value < 0.01:
            return 'moderate'
        elif p_value < 0.05:
            return 'minor'
        else:
            return 'none'
    
    def _interpret_psi(self, psi: float) -> str:
        """Interpret PSI value"""
        if psi < 0.1:
            return 'no_drift'
        elif psi < 0.2:
            return 'minor_drift'
        elif psi < 0.5:
            return 'moderate_drift'
        else:
            return 'severe_drift'
    
    def generate_drift_report(self, feature_drift: Dict, 
                            psi_results: Dict,
                            prediction_drift: Optional[Dict] = None,
                            concept_drift: Optional[Dict] = None) -> Dict:
        """
        Generate comprehensive drift report
        
        Args:
            feature_drift: Feature drift results
            psi_results: PSI results
            prediction_drift: Prediction drift results (optional)
            concept_drift: Concept drift results (optional)
            
        Returns:
            Comprehensive drift report
        """
        report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'feature_drift': feature_drift,
            'psi_results': psi_results,
            'prediction_drift': prediction_drift,
            'concept_drift': concept_drift,
            'summary': {}
        }
        
        # Count drift detections
        feature_drift_count = sum(1 for result in feature_drift.values() 
                                if result.get('drift_detected', False))
        severe_feature_drift = sum(1 for result in feature_drift.values() 
                                 if result.get('severity') == 'severe')
        
        psi_drift_count = sum(1 for result in psi_results.values() 
                            if result.get('drift_level') in ['moderate_drift', 'severe_drift'])
        
        report['summary'] = {
            'total_features': len(feature_drift),
            'features_with_drift': feature_drift_count,
            'severe_feature_drift': severe_feature_drift,
            'features_with_psi_drift': psi_drift_count,
            'prediction_drift_detected': prediction_drift.get('drift_detected', False) if prediction_drift else False,
            'concept_drift_detected': concept_drift.get('drift_detected', False) if concept_drift else False
        }
        
        return report
```

### Integration with Retraining Pipeline

#### Updated Task in dags/sales_forecast_retraining.py
```python
@task
def validate_data_quality(data_info: dict):
    """Validate data quality and check for drift"""
    if data_info["status"] == "not_available":
        return {"status": "skipped", "reason": "No new data"}
    
    from include.data_validation.data_validator import DataValidator
    from include.ml_models.drift_detection import DriftDetector
    import pandas as pd
    
    # Load data
    data_path = f"/opt/airflow/include/data/sales_data_{data_info['date']}.parquet"
    df = pd.read_parquet(data_path)
    
    # Validate data
    validator = DataValidator()
    validation_results = validator.validate_sales_data(df)
    
    # Check for drift
    reference_path = "/opt/airflow/include/data/reference_data.parquet"
    reference_df = pd.read_parquet(reference_path)
    
    # Initialize drift detector
    drift_detector = DriftDetector(reference_df)
    
    # Detect feature drift
    feature_drift = drift_detector.detect_feature_drift(df)
    
    # Calculate PSI
    psi_results = drift_detector.calculate_psi(df)
    
    # Generate drift report
    drift_report = drift_detector.generate_drift_report(feature_drift, psi_results)
    
    # Determine if retraining is needed based on drift
    drift_severity = drift_report['summary']['severe_feature_drift']
    features_with_drift = drift_report['summary']['features_with_drift']
    
    # Trigger retraining if significant drift detected
    should_retrain = drift_severity > 0 or features_with_drift > (len(feature_drift) * 0.3)
    
    return {
        "validation_results": validation_results,
        "drift_report": drift_report,
        "should_retrain": should_retrain,
        "quality_score": validation_results["quality_score"]
    }
```

### Monitoring and Alerting

#### Prometheus Metrics for Drift
```python
# In drift_detection.py or in the Airflow task
from prometheus_client import Gauge, Counter

# Define metrics
feature_drift_detected = Gauge('feature_drift_detected', 'Number of features with drift', ['feature'])
psi_drift_level = Gauge('psi_drift_level', 'PSI drift level', ['feature'])
prediction_drift_detected = Gauge('prediction_drift_detected', 'Prediction drift detected', ['model'])
concept_drift_detected = Gauge('concept_drift_detected', 'Concept drift detected')

def update_drift_metrics(drift_report: Dict):
    """Update Prometheus metrics with drift detection results"""
    try:
        # Update feature drift metrics
        for feature, result in drift_report.get('feature_drift', {}).items():
            if result.get('drift_detected', False):
                feature_drift_detected.labels(feature=feature).set(1)
            else:
                feature_drift_detected.labels(feature=feature).set(0)
        
        # Update PSI metrics
        for feature, result in drift_report.get('psi_results', {}).items():
            drift_level = result.get('drift_level', 'no_drift')
            level_value = {'no_drift': 0, 'minor_drift': 1, 'moderate_drift': 2, 'severe_drift': 3}
            psi_drift_level.labels(feature=feature).set(level_value.get(drift_level, 0))
        
        # Update prediction drift metric
        if drift_report.get('prediction_drift', {}).get('drift_detected', False):
            prediction_drift_detected.labels(model='sales_forecast').set(1)
        else:
            prediction_drift_detected.labels(model='sales_forecast').set(0)
            
        # Update concept drift metric
        if drift_report.get('concept_drift', {}).get('drift_detected', False):
            concept_drift_detected.set(1)
        else:
            concept_drift_detected.set(0)
            
    except Exception as e:
        logger.error(f"Error updating drift metrics: {e}")
```

#### Grafana Dashboard for Drift
```json
{
  "dashboard": {
    "id": null,
    "title": "Model Drift Monitoring",
    "timezone": "browser",
    "schemaVersion": 16,
    "version": 0,
    "refresh": "5s",
    "panels": [
      {
        "id": 1,
        "type": "stat",
        "title": "Features with Drift",
        "gridPos": {
          "x": 0,
          "y": 0,
          "w": 6,
          "h": 4
        },
        "targets": [
          {
            "expr": "sum(feature_drift_detected)",
            "format": "time_series",
            "intervalFactor": 1,
            "refId": "A"
          }
        ]
      },
      {
        "id": 2,
        "type": "graph",
        "title": "Feature Drift Over Time",
        "gridPos": {
          "x": 6,
          "y": 0,
          "w": 12,
          "h": 8
        },
        "targets": [
          {
            "expr": "feature_drift_detected",
            "format": "time_series",
            "intervalFactor": 1,
            "refId": "A"
          }
        ]
      },
      {
        "id": 3,
        "type": "table",
        "title": "PSI Drift Levels",
        "gridPos": {
          "x": 0,
          "y": 4,
          "w": 6,
          "h": 4
        },
        "targets": [
          {
            "expr": "psi_drift_level",
            "format": "table",
            "intervalFactor": 1,
            "refId": "A"
          }
        ]
      }
    ]
  }
}
```

## Testing Plan

### Unit Tests
- Test drift detection methods
- Test PSI calculation
- Test prediction drift detection
- Test concept drift detection

### Integration Tests
- Test drift detection in retraining pipeline
- Test metrics collection
- Test dashboard visualization

### Manual Testing
- Run drift detection on sample data
- Verify metrics in Prometheus
- Check dashboard in Grafana
- Test alerting functionality

## Success Criteria

1. Drift detection module is implemented
2. Feature drift is detected using KS test
3. PSI is calculated for numerical features
4. Prediction drift is monitored
5. Concept drift is detected
6. Drift metrics are collected in Prometheus
7. Grafana dashboard displays drift information
8. Integration with retraining pipeline works

## Dependencies

- scipy (for statistical tests)
- numpy (for numerical calculations)
- pandas (for data handling)
- prometheus-client (for metrics)
- Existing project structure

## Timeline Estimate

| Task | Estimated Time |
|------|----------------|
| Create Drift Detection Module | 3 hours |
| Integrate with Retraining Pipeline | 2 hours |
| Add Monitoring and Alerting | 2 hours |
| Testing | 2 hours |
| **Total** | **9 hours** |