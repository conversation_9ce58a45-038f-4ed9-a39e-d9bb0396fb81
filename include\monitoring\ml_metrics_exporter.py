"""
ML Pipeline Metrics Exporter for Prometheus
"""

import os
import time
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
from prometheus_client import (
    CollectorRegistry, Gauge, Counter, Histogram, Info, 
    generate_latest, CONTENT_TYPE_LATEST, start_http_server
)
import mlflow
from mlflow.tracking import MlflowClient

logger = logging.getLogger(__name__)


class MLMetricsExporter:
    """
    Comprehensive ML pipeline metrics exporter for Prometheus monitoring
    """
    
    def __init__(self, port: int = 8080, update_interval: int = 60):
        self.port = port
        self.update_interval = update_interval
        self.registry = CollectorRegistry()
        self.running = False
        
        # Initialize MLflow client
        self.mlflow_client = MlflowClient()
        
        # Initialize metrics
        self._init_metrics()
        
        # Start background update thread
        self.update_thread = None
        
    def _init_metrics(self):
        """Initialize Prometheus metrics"""
        
        # Model Performance Metrics
        self.model_predictions_total = Counter(
            'model_predictions_total',
            'Total number of model predictions',
            ['model_name', 'model_version', 'status'],
            registry=self.registry
        )
        
        self.model_prediction_duration = Histogram(
            'model_prediction_duration_seconds',
            'Time spent on model predictions',
            ['model_name', 'model_version'],
            buckets=[0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0],
            registry=self.registry
        )
        
        self.model_prediction_errors = Counter(
            'model_prediction_errors_total',
            'Total number of model prediction errors',
            ['model_name', 'model_version', 'error_type'],
            registry=self.registry
        )
        
        self.model_accuracy = Gauge(
            'model_accuracy_score',
            'Current model accuracy score',
            ['model_name', 'model_version', 'metric_type'],
            registry=self.registry
        )
        
        # Training Pipeline Metrics
        self.training_duration = Histogram(
            'training_duration_seconds',
            'Time spent on model training',
            ['model_name', 'training_type'],
            buckets=[60, 300, 600, 1800, 3600, 7200, 14400],  # 1min to 4hours
            registry=self.registry
        )
        
        self.training_jobs_total = Counter(
            'training_jobs_total',
            'Total number of training jobs',
            ['model_name', 'status'],
            registry=self.registry
        )
        
        self.model_versions_total = Gauge(
            'model_versions_total',
            'Total number of model versions',
            ['model_name', 'stage'],
            registry=self.registry
        )
        
        # Data Quality Metrics
        self.data_quality_score = Gauge(
            'data_quality_score',
            'Current data quality score',
            ['dataset_name', 'quality_dimension'],
            registry=self.registry
        )
        
        self.data_missing_values = Gauge(
            'data_missing_values_total',
            'Number of missing values in dataset',
            ['dataset_name', 'column_name'],
            registry=self.registry
        )
        
        self.data_last_update = Gauge(
            'data_last_update_timestamp',
            'Timestamp of last data update',
            ['dataset_name'],
            registry=self.registry
        )
        
        # Drift Detection Metrics (imported from drift_detection.py)
        try:
            from ml_models.drift_detection import (
                feature_drift_detected, psi_drift_score,
                prediction_drift_detected, concept_drift_detected,
                drift_alerts_triggered, drift_detection_duration
            )
        except ImportError as e:
            logger.warning(f"Could not import drift detection metrics: {e}")
            # Create placeholder metrics if drift detection is not available
            feature_drift_detected = None
            psi_drift_score = None
            prediction_drift_detected = None
            concept_drift_detected = None
            drift_alerts_triggered = None
            drift_detection_duration = None
        
        # System Resource Metrics
        self.memory_usage = Gauge(
            'ml_pipeline_memory_usage_bytes',
            'Memory usage of ML pipeline components',
            ['component'],
            registry=self.registry
        )
        
        self.cpu_usage = Gauge(
            'ml_pipeline_cpu_usage_percent',
            'CPU usage of ML pipeline components',
            ['component'],
            registry=self.registry
        )
        
        # Business Metrics
        self.prediction_volume = Counter(
            'prediction_volume_total',
            'Total volume of predictions made',
            ['model_name', 'prediction_type'],
            registry=self.registry
        )
        
        self.model_business_impact = Gauge(
            'model_business_impact_score',
            'Business impact score of model predictions',
            ['model_name', 'impact_type'],
            registry=self.registry
        )
        
        # Pipeline Health Metrics
        self.pipeline_health_score = Gauge(
            'pipeline_health_score',
            'Overall health score of ML pipeline',
            ['component'],
            registry=self.registry
        )
        
        self.service_availability = Gauge(
            'service_availability',
            'Availability of ML pipeline services',
            ['service_name'],
            registry=self.registry
        )
        
        # Model Information
        self.model_info = Info(
            'model_info',
            'Information about deployed models',
            registry=self.registry
        )
        
    def start_server(self):
        """Start the metrics HTTP server"""
        try:
            start_http_server(self.port, registry=self.registry)
            logger.info(f"ML metrics server started on port {self.port}")
            
            # Start background metrics update
            self.running = True
            self.update_thread = threading.Thread(target=self._update_metrics_loop)
            self.update_thread.daemon = True
            self.update_thread.start()
            
        except Exception as e:
            logger.error(f"Failed to start metrics server: {e}")
            raise
    
    def stop_server(self):
        """Stop the metrics server"""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("ML metrics server stopped")
    
    def _update_metrics_loop(self):
        """Background loop to update metrics"""
        while self.running:
            try:
                self.update_all_metrics()
                time.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error updating metrics: {e}")
                time.sleep(self.update_interval)
    
    def update_all_metrics(self):
        """Update all metrics from various sources"""
        logger.debug("Updating ML pipeline metrics...")
        
        try:
            # Update MLflow-based metrics
            self._update_mlflow_metrics()
            
            # Update model performance metrics
            self._update_model_performance_metrics()
            
            # Update data quality metrics
            self._update_data_quality_metrics()
            
            # Update system resource metrics
            self._update_system_metrics()
            
            # Update pipeline health metrics
            self._update_pipeline_health_metrics()
            
        except Exception as e:
            logger.error(f"Failed to update metrics: {e}")
    
    def _update_mlflow_metrics(self):
        """Update metrics from MLflow"""
        try:
            # Get all experiments
            experiments = self.mlflow_client.search_experiments()
            
            for experiment in experiments:
                if 'sales_forecast' in experiment.name.lower():
                    # Get recent runs
                    runs = self.mlflow_client.search_runs(
                        experiment_ids=[experiment.experiment_id],
                        max_results=10,
                        order_by=["start_time DESC"]
                    )
                    
                    for run in runs:
                        self._process_mlflow_run(run)
                        
        except Exception as e:
            logger.warning(f"Failed to update MLflow metrics: {e}")
    
    def _process_mlflow_run(self, run):
        """Process individual MLflow run for metrics"""
        try:
            run_data = run.data
            run_info = run.info
            
            # Extract model name from tags or run name
            model_name = run_data.tags.get('model_name', 'unknown')
            model_version = run_data.tags.get('model_version', 'unknown')
            
            # Update training metrics
            if run_info.status == 'FINISHED':
                self.training_jobs_total.labels(
                    model_name=model_name, 
                    status='success'
                ).inc()
                
                # Update training duration
                if run_info.end_time and run_info.start_time:
                    duration = (run_info.end_time - run_info.start_time) / 1000  # Convert to seconds
                    self.training_duration.labels(
                        model_name=model_name,
                        training_type='batch'
                    ).observe(duration)
            
            elif run_info.status == 'FAILED':
                self.training_jobs_total.labels(
                    model_name=model_name,
                    status='failed'
                ).inc()
            
            # Update model accuracy metrics
            for metric_name, metric_value in run_data.metrics.items():
                if metric_name in ['r2', 'rmse', 'mae', 'mape']:
                    self.model_accuracy.labels(
                        model_name=model_name,
                        model_version=model_version,
                        metric_type=metric_name
                    ).set(metric_value)
                    
        except Exception as e:
            logger.warning(f"Failed to process MLflow run: {e}")
    
    def _update_model_performance_metrics(self):
        """Update model performance metrics"""
        try:
            # This would typically connect to your model serving infrastructure
            # For now, we'll simulate some metrics
            
            # Get model versions from MLflow registry
            try:
                models = self.mlflow_client.search_registered_models()
                
                for model in models:
                    if 'sales_forecast' in model.name.lower():
                        versions = self.mlflow_client.search_model_versions(f"name='{model.name}'")
                        
                        # Count versions by stage
                        stage_counts = {}
                        for version in versions:
                            stage = version.current_stage
                            stage_counts[stage] = stage_counts.get(stage, 0) + 1
                        
                        for stage, count in stage_counts.items():
                            self.model_versions_total.labels(
                                model_name=model.name,
                                stage=stage
                            ).set(count)
                            
            except Exception as e:
                logger.warning(f"Failed to update model version metrics: {e}")
                
        except Exception as e:
            logger.warning(f"Failed to update model performance metrics: {e}")
    
    def _update_data_quality_metrics(self):
        """Update data quality metrics"""
        try:
            # This would typically connect to your data quality monitoring system
            # For now, we'll set some example metrics
            
            current_time = time.time()
            
            # Simulate data quality scores
            datasets = ['sales_data', 'customer_data', 'product_data']
            quality_dimensions = ['completeness', 'accuracy', 'consistency', 'timeliness']
            
            for dataset in datasets:
                for dimension in quality_dimensions:
                    # Simulate quality score between 0.7 and 1.0
                    score = 0.7 + (hash(f"{dataset}_{dimension}") % 100) / 333.0
                    self.data_quality_score.labels(
                        dataset_name=dataset,
                        quality_dimension=dimension
                    ).set(score)
                
                # Set last update timestamp
                self.data_last_update.labels(dataset_name=dataset).set(current_time)
                
        except Exception as e:
            logger.warning(f"Failed to update data quality metrics: {e}")
    
    def _update_system_metrics(self):
        """Update system resource metrics"""
        try:
            import psutil
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.memory_usage.labels(component='ml_pipeline').set(memory.used)
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.labels(component='ml_pipeline').set(cpu_percent)
            
        except ImportError:
            # psutil not available, skip system metrics
            pass
        except Exception as e:
            logger.warning(f"Failed to update system metrics: {e}")
    
    def _update_pipeline_health_metrics(self):
        """Update pipeline health metrics"""
        try:
            # Calculate overall pipeline health based on various factors
            components = ['training', 'inference', 'monitoring', 'data_pipeline']
            
            for component in components:
                # Simulate health score based on component status
                health_score = 0.8 + (hash(component) % 100) / 500.0  # 0.8 to 1.0
                self.pipeline_health_score.labels(component=component).set(health_score)
            
            # Service availability
            services = ['mlflow', 'airflow', 'streamlit', 'prometheus']
            for service in services:
                # In a real implementation, you'd check actual service health
                availability = 1.0  # Assume available
                self.service_availability.labels(service_name=service).set(availability)
                
        except Exception as e:
            logger.warning(f"Failed to update pipeline health metrics: {e}")
    
    def record_prediction(self, model_name: str, model_version: str, 
                         duration: float, status: str = 'success'):
        """Record a model prediction"""
        self.model_predictions_total.labels(
            model_name=model_name,
            model_version=model_version,
            status=status
        ).inc()
        
        self.model_prediction_duration.labels(
            model_name=model_name,
            model_version=model_version
        ).observe(duration)
    
    def record_prediction_error(self, model_name: str, model_version: str, error_type: str):
        """Record a prediction error"""
        self.model_prediction_errors.labels(
            model_name=model_name,
            model_version=model_version,
            error_type=error_type
        ).inc()
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format"""
        return generate_latest(self.registry)


# Global metrics exporter instance
metrics_exporter = None


def start_metrics_server(port: int = 8080, update_interval: int = 60):
    """Start the ML metrics server"""
    global metrics_exporter
    
    if metrics_exporter is None:
        metrics_exporter = MLMetricsExporter(port=port, update_interval=update_interval)
        metrics_exporter.start_server()
        logger.info(f"ML metrics server started on port {port}")
    else:
        logger.warning("Metrics server already running")


def stop_metrics_server():
    """Stop the ML metrics server"""
    global metrics_exporter
    
    if metrics_exporter:
        metrics_exporter.stop_server()
        metrics_exporter = None
        logger.info("ML metrics server stopped")


def get_metrics_exporter() -> Optional[MLMetricsExporter]:
    """Get the global metrics exporter instance"""
    return metrics_exporter


if __name__ == "__main__":
    # Start the metrics server
    logging.basicConfig(level=logging.INFO)
    start_metrics_server(port=8080, update_interval=30)
    
    try:
        # Keep the server running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down metrics server...")
        stop_metrics_server()
