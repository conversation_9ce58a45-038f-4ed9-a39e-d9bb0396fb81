"""
Model Versioning and Comparison System
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import mlflow
from mlflow.tracking import MlflowClient

logger = logging.getLogger(__name__)


class ModelVersionManager:
    """
    Manages model versions, comparisons, and deployment decisions
    """
    
    def __init__(self, tracking_uri: Optional[str] = None):
        self.tracking_uri = tracking_uri or os.getenv('MLFLOW_TRACKING_URI', 'http://localhost:5000')
        mlflow.set_tracking_uri(self.tracking_uri)
        self.client = MlflowClient(tracking_uri=self.tracking_uri)
        
    def get_model_versions(self, model_name: str, stage: Optional[str] = None) -> List[Dict]:
        """
        Get all versions of a model, optionally filtered by stage
        """
        try:
            versions = self.client.search_model_versions(f"name='{model_name}'")
            
            if stage:
                versions = [v for v in versions if v.current_stage == stage]
            
            version_info = []
            for version in versions:
                run = self.client.get_run(version.run_id)
                
                version_data = {
                    'version': version.version,
                    'stage': version.current_stage,
                    'run_id': version.run_id,
                    'creation_timestamp': version.creation_timestamp,
                    'last_updated_timestamp': version.last_updated_timestamp,
                    'metrics': run.data.metrics,
                    'params': run.data.params,
                    'tags': run.data.tags
                }
                version_info.append(version_data)
            
            # Sort by version number (descending)
            version_info.sort(key=lambda x: int(x['version']), reverse=True)
            
            return version_info
            
        except Exception as e:
            logger.error(f"Failed to get model versions for {model_name}: {e}")
            return []
    
    def compare_model_versions(self, model_name: str, version1: str, version2: str) -> Dict:
        """
        Compare two model versions across multiple metrics
        """
        try:
            versions = self.get_model_versions(model_name)
            
            v1_data = next((v for v in versions if v['version'] == version1), None)
            v2_data = next((v for v in versions if v['version'] == version2), None)
            
            if not v1_data or not v2_data:
                return {'error': 'One or both versions not found'}
            
            comparison = {
                'version1': version1,
                'version2': version2,
                'comparison_timestamp': datetime.now().isoformat(),
                'metrics_comparison': {},
                'params_comparison': {},
                'performance_summary': {}
            }
            
            # Compare metrics
            v1_metrics = v1_data['metrics']
            v2_metrics = v2_data['metrics']
            
            for metric in set(v1_metrics.keys()) | set(v2_metrics.keys()):
                v1_value = v1_metrics.get(metric, None)
                v2_value = v2_metrics.get(metric, None)
                
                if v1_value is not None and v2_value is not None:
                    difference = v2_value - v1_value
                    percent_change = (difference / v1_value) * 100 if v1_value != 0 else 0
                    
                    comparison['metrics_comparison'][metric] = {
                        'version1_value': v1_value,
                        'version2_value': v2_value,
                        'difference': difference,
                        'percent_change': percent_change,
                        'better_version': version2 if self._is_metric_better(metric, v2_value, v1_value) else version1
                    }
            
            # Compare parameters
            v1_params = v1_data['params']
            v2_params = v2_data['params']
            
            for param in set(v1_params.keys()) | set(v2_params.keys()):
                comparison['params_comparison'][param] = {
                    'version1_value': v1_params.get(param, 'N/A'),
                    'version2_value': v2_params.get(param, 'N/A'),
                    'changed': v1_params.get(param) != v2_params.get(param)
                }
            
            # Generate performance summary
            comparison['performance_summary'] = self._generate_performance_summary(comparison['metrics_comparison'])
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to compare model versions: {e}")
            return {'error': str(e)}
    
    def _is_metric_better(self, metric_name: str, new_value: float, old_value: float) -> bool:
        """
        Determine if a new metric value is better than the old one
        """
        # Lower is better for these metrics
        lower_is_better = ['rmse', 'mae', 'mse', 'mape', 'loss']
        
        if any(metric in metric_name.lower() for metric in lower_is_better):
            return new_value < old_value
        else:
            # Higher is better for metrics like r2, accuracy, etc.
            return new_value > old_value
    
    def _generate_performance_summary(self, metrics_comparison: Dict) -> Dict:
        """
        Generate a summary of performance comparison
        """
        if not metrics_comparison:
            return {'status': 'no_metrics', 'recommendation': 'insufficient_data'}
        
        improvements = 0
        degradations = 0
        total_metrics = len(metrics_comparison)
        
        key_metrics = ['rmse', 'mae', 'r2', 'mape']
        key_improvements = 0
        key_degradations = 0
        key_metrics_count = 0
        
        for metric, comparison in metrics_comparison.items():
            if comparison['better_version'] == comparison.get('version2'):
                improvements += 1
                if any(key in metric.lower() for key in key_metrics):
                    key_improvements += 1
                    key_metrics_count += 1
            else:
                degradations += 1
                if any(key in metric.lower() for key in key_metrics):
                    key_degradations += 1
                    key_metrics_count += 1
        
        improvement_ratio = improvements / total_metrics if total_metrics > 0 else 0
        key_improvement_ratio = key_improvements / key_metrics_count if key_metrics_count > 0 else 0
        
        # Generate recommendation
        if key_improvement_ratio > 0.6:  # 60% of key metrics improved
            recommendation = 'deploy'
            confidence = 'high' if key_improvement_ratio > 0.8 else 'medium'
        elif key_improvement_ratio > 0.4:  # 40-60% improved
            recommendation = 'deploy_with_caution'
            confidence = 'medium'
        else:
            recommendation = 'reject'
            confidence = 'high'
        
        return {
            'total_metrics': total_metrics,
            'improvements': improvements,
            'degradations': degradations,
            'improvement_ratio': improvement_ratio,
            'key_metrics_count': key_metrics_count,
            'key_improvements': key_improvements,
            'key_degradations': key_degradations,
            'key_improvement_ratio': key_improvement_ratio,
            'recommendation': recommendation,
            'confidence': confidence
        }
    
    def get_production_model_info(self, model_name: str) -> Optional[Dict]:
        """
        Get information about the current production model
        """
        production_versions = self.get_model_versions(model_name, stage='Production')
        
        if not production_versions:
            return None
        
        # Return the latest production version
        return production_versions[0]
    
    def promote_model_to_production(self, model_name: str, version: str, 
                                  archive_current: bool = True) -> Dict:
        """
        Promote a model version to production
        """
        try:
            # Archive current production model if requested
            if archive_current:
                current_production = self.get_production_model_info(model_name)
                if current_production:
                    self.client.transition_model_version_stage(
                        name=model_name,
                        version=current_production['version'],
                        stage='Archived'
                    )
            
            # Promote new version to production
            self.client.transition_model_version_stage(
                name=model_name,
                version=version,
                stage='Production'
            )
            
            return {
                'status': 'success',
                'model_name': model_name,
                'promoted_version': version,
                'promotion_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to promote model to production: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def rollback_model(self, model_name: str, target_version: Optional[str] = None) -> Dict:
        """
        Rollback to a previous model version
        """
        try:
            if target_version:
                # Rollback to specific version
                rollback_version = target_version
            else:
                # Rollback to previous production version
                archived_versions = self.get_model_versions(model_name, stage='Archived')
                if not archived_versions:
                    return {'status': 'failed', 'error': 'No archived versions available for rollback'}
                
                # Get the most recently archived version
                rollback_version = archived_versions[0]['version']
            
            # Archive current production
            current_production = self.get_production_model_info(model_name)
            if current_production:
                self.client.transition_model_version_stage(
                    name=model_name,
                    version=current_production['version'],
                    stage='Archived'
                )
            
            # Promote rollback version to production
            self.client.transition_model_version_stage(
                name=model_name,
                version=rollback_version,
                stage='Production'
            )
            
            return {
                'status': 'success',
                'model_name': model_name,
                'rollback_version': rollback_version,
                'previous_production_version': current_production['version'] if current_production else None,
                'rollback_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to rollback model: {e}")
            return {
                'status': 'failed',
                'error': str(e)
            }
    
    def get_model_performance_history(self, model_name: str, days: int = 30) -> Dict:
        """
        Get performance history for a model over time
        """
        try:
            versions = self.get_model_versions(model_name)
            
            # Filter versions from the last N days
            cutoff_time = datetime.now() - timedelta(days=days)
            cutoff_timestamp = int(cutoff_time.timestamp() * 1000)  # MLflow uses milliseconds
            
            recent_versions = [
                v for v in versions 
                if v['creation_timestamp'] >= cutoff_timestamp
            ]
            
            # Extract performance metrics over time
            performance_data = []
            for version in recent_versions:
                performance_data.append({
                    'version': version['version'],
                    'timestamp': datetime.fromtimestamp(version['creation_timestamp'] / 1000).isoformat(),
                    'stage': version['stage'],
                    'metrics': version['metrics']
                })
            
            # Sort by timestamp
            performance_data.sort(key=lambda x: x['timestamp'])
            
            return {
                'model_name': model_name,
                'period_days': days,
                'versions_count': len(performance_data),
                'performance_history': performance_data
            }
            
        except Exception as e:
            logger.error(f"Failed to get model performance history: {e}")
            return {'error': str(e)}


# Export the main class
__all__ = ['ModelVersionManager']
