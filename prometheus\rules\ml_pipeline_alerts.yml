groups:
  - name: ml_pipeline_alerts
    rules:
      # Model Drift Alerts
      - alert: HighFeatureDrift
        expr: feature_drift_detected > 0.3
        for: 5m
        labels:
          severity: warning
          component: drift_detection
        annotations:
          summary: "High feature drift detected"
          description: "Feature drift score {{ $value }} exceeds threshold of 0.3 for feature {{ $labels.feature_name }}"

      - alert: SevereFeatureDrift
        expr: feature_drift_detected > 0.5
        for: 2m
        labels:
          severity: critical
          component: drift_detection
        annotations:
          summary: "Severe feature drift detected"
          description: "Feature drift score {{ $value }} exceeds critical threshold of 0.5 for feature {{ $labels.feature_name }}"

      - alert: ConceptDriftDetected
        expr: concept_drift_detected > 0.2
        for: 5m
        labels:
          severity: warning
          component: drift_detection
        annotations:
          summary: "Concept drift detected"
          description: "Concept drift score {{ $value }} exceeds threshold of 0.2"

      - alert: PredictionDriftDetected
        expr: prediction_drift_detected > 0.25
        for: 5m
        labels:
          severity: warning
          component: drift_detection
        annotations:
          summary: "Prediction drift detected"
          description: "Prediction drift score {{ $value }} exceeds threshold of 0.25"

      # Model Performance Alerts
      - alert: ModelPerformanceDegradation
        expr: increase(model_prediction_errors_total[1h]) > 100
        for: 10m
        labels:
          severity: warning
          component: model_performance
        annotations:
          summary: "Model prediction errors increasing"
          description: "Model {{ $labels.model_name }} has {{ $value }} prediction errors in the last hour"

      - alert: HighPredictionLatency
        expr: histogram_quantile(0.95, rate(model_prediction_duration_seconds_bucket[5m])) > 1.0
        for: 5m
        labels:
          severity: warning
          component: model_performance
        annotations:
          summary: "High model prediction latency"
          description: "95th percentile prediction latency is {{ $value }}s for model {{ $labels.model_name }}"

      - alert: ModelUnavailable
        expr: up{job="ml-pipeline-metrics"} == 0
        for: 2m
        labels:
          severity: critical
          component: model_availability
        annotations:
          summary: "ML model service is down"
          description: "ML model service has been down for more than 2 minutes"

      # Training Pipeline Alerts
      - alert: TrainingJobFailed
        expr: increase(airflow_dag_run_failed_total{dag_id="sales_forecast_training"}[1h]) > 0
        for: 1m
        labels:
          severity: critical
          component: training_pipeline
        annotations:
          summary: "Training job failed"
          description: "Sales forecast training DAG has failed {{ $value }} times in the last hour"

      - alert: RetrainingJobFailed
        expr: increase(airflow_dag_run_failed_total{dag_id="sales_forecast_retraining"}[1h]) > 0
        for: 1m
        labels:
          severity: critical
          component: retraining_pipeline
        annotations:
          summary: "Retraining job failed"
          description: "Sales forecast retraining DAG has failed {{ $value }} times in the last hour"

      - alert: LongRunningTrainingJob
        expr: time() - airflow_dag_run_start_time{dag_id="sales_forecast_training"} > 14400  # 4 hours
        for: 5m
        labels:
          severity: warning
          component: training_pipeline
        annotations:
          summary: "Training job running too long"
          description: "Training job has been running for more than 4 hours"

      # Data Quality Alerts
      - alert: DataQualityIssues
        expr: data_quality_score < 0.7
        for: 5m
        labels:
          severity: warning
          component: data_quality
        annotations:
          summary: "Data quality issues detected"
          description: "Data quality score {{ $value }} is below threshold of 0.7"

      - alert: MissingData
        expr: increase(data_missing_values_total[1h]) > 1000
        for: 10m
        labels:
          severity: warning
          component: data_quality
        annotations:
          summary: "High number of missing values"
          description: "{{ $value }} missing values detected in the last hour"

      # System Resource Alerts
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          component: system_resources
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 10m
        labels:
          severity: warning
          component: system_resources
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 10
        for: 5m
        labels:
          severity: critical
          component: system_resources
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value }}% available on {{ $labels.instance }}"

      # MLflow Alerts
      - alert: MLflowServiceDown
        expr: up{job="mlflow"} == 0
        for: 2m
        labels:
          severity: critical
          component: mlflow
        annotations:
          summary: "MLflow service is down"
          description: "MLflow tracking server has been down for more than 2 minutes"

      # Airflow Alerts
      - alert: AirflowSchedulerDown
        expr: up{job="airflow-scheduler"} == 0
        for: 2m
        labels:
          severity: critical
          component: airflow
        annotations:
          summary: "Airflow scheduler is down"
          description: "Airflow scheduler has been down for more than 2 minutes"

      - alert: AirflowWebserverDown
        expr: up{job="airflow-webserver"} == 0
        for: 2m
        labels:
          severity: critical
          component: airflow
        annotations:
          summary: "Airflow webserver is down"
          description: "Airflow webserver has been down for more than 2 minutes"

      # Streamlit UI Alerts
      - alert: StreamlitUIDown
        expr: up{job="streamlit-ui"} == 0
        for: 2m
        labels:
          severity: warning
          component: streamlit_ui
        annotations:
          summary: "Streamlit UI is down"
          description: "Streamlit UI has been down for more than 2 minutes"

      - alert: HighUIErrorRate
        expr: rate(streamlit_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          component: streamlit_ui
        annotations:
          summary: "High error rate in Streamlit UI"
          description: "Error rate is {{ $value }} errors per second in Streamlit UI"

  - name: ml_pipeline_performance
    rules:
      # Recording rules for performance metrics
      - record: ml:prediction_latency_p95
        expr: histogram_quantile(0.95, rate(model_prediction_duration_seconds_bucket[5m]))

      - record: ml:prediction_latency_p99
        expr: histogram_quantile(0.99, rate(model_prediction_duration_seconds_bucket[5m]))

      - record: ml:prediction_error_rate
        expr: rate(model_prediction_errors_total[5m]) / rate(model_predictions_total[5m])

      - record: ml:drift_detection_rate
        expr: rate(drift_alerts_triggered_total[1h])

      - record: ml:training_success_rate
        expr: rate(airflow_dag_run_success_total{dag_id=~"sales_forecast_.*"}[24h]) / rate(airflow_dag_run_total{dag_id=~"sales_forecast_.*"}[24h])

      - record: ml:data_quality_avg
        expr: avg_over_time(data_quality_score[1h])

  - name: ml_pipeline_sla
    rules:
      # SLA monitoring rules
      - alert: PredictionSLAViolation
        expr: ml:prediction_latency_p95 > 0.5  # 500ms SLA
        for: 5m
        labels:
          severity: warning
          component: sla
        annotations:
          summary: "Prediction SLA violation"
          description: "95th percentile prediction latency {{ $value }}s exceeds SLA of 500ms"

      - alert: TrainingSLAViolation
        expr: time() - airflow_dag_run_start_time{dag_id="sales_forecast_training"} > 21600  # 6 hours SLA
        for: 1m
        labels:
          severity: critical
          component: sla
        annotations:
          summary: "Training SLA violation"
          description: "Training job has exceeded 6-hour SLA"

      - alert: DataFreshnessSLAViolation
        expr: time() - data_last_update_timestamp > 86400  # 24 hours
        for: 10m
        labels:
          severity: warning
          component: sla
        annotations:
          summary: "Data freshness SLA violation"
          description: "Data has not been updated for more than 24 hours"
