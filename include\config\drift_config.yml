# Drift Detection Configuration
drift_detection:
  # Enable/disable drift detection
  enabled: true
  
  # Detection methods configuration
  methods:
    feature_drift:
      enabled: true
      method: "kolmogorov_smirnov"
      threshold: 0.05
    
    psi_calculation:
      enabled: true
      buckets: 10
      thresholds:
        minor: 0.1
        moderate: 0.2
        severe: 0.5
    
    prediction_drift:
      enabled: true
      method: "kolmogorov_smirnov"
      threshold: 0.05
    
    concept_drift:
      enabled: true
      method: "kolmogorov_smirnov"
      threshold: 0.05

# Monitoring Configuration
monitoring:
  # General monitoring settings
  enabled: true
  check_interval_minutes: 60
  reference_data_update_hours: 24
  max_history_days: 30
  
  # Prometheus metrics
  prometheus:
    enabled: true
    port: 8000
    metrics_prefix: "sales_forecast_"
    push_gateway:
      enabled: false
      url: "http://prometheus-pushgateway:9091"
      job: "drift_detection"

# Alerting Configuration
alerting:
  # Enable/disable alerting
  enabled: true
  
  # Alert channels
  channels:
    - "prometheus"
    - "log"
    - "mlflow"
  
  # Severity levels to alert on
  severity_levels:
    - "minor"
    - "moderate"
    - "severe"
  
  # Cooldown period between alerts (minutes)
  cooldown_minutes: 60
  
  # Alert thresholds
  thresholds:
    feature_drift:
      minor: 0.05
      moderate: 0.01
      severe: 0.001
    
    psi_drift:
      minor: 0.1
      moderate: 0.2
      severe: 0.5
    
    prediction_drift:
      minor: 0.05
      moderate: 0.01
      severe: 0.001
    
    concept_drift:
      minor: 0.05
      moderate: 0.01
      severe: 0.001

# MLflow Integration
mlflow:
  experiment_name: "drift_monitoring"
  log_detailed_results: true
  log_artifacts: true
  
  # Model registry integration
  model_registry:
    enabled: true
    stage_filter: "Production"
    
# Data Configuration
data:
  # Reference data settings
  reference_data:
    update_frequency_hours: 24
    min_samples: 1000
    max_age_days: 30
  
  # Current data validation
  current_data:
    min_samples: 100
    required_columns:
      - "date"
      - "sales"
      - "store_id"
    
    # Data quality checks
    quality_checks:
      missing_values_threshold: 0.1
      outlier_detection: true
      outlier_method: "iqr"
      outlier_threshold: 3.0

# Feature Configuration
features:
  # Features to monitor for drift
  monitor_features:
    - "sales"
    - "quantity_sold"
    - "profit"
    - "customer_traffic"
    - "has_promotion"
    - "is_holiday"
  
  # Feature groups for grouped analysis
  feature_groups:
    sales_metrics:
      - "sales"
      - "quantity_sold"
      - "profit"
    
    external_factors:
      - "customer_traffic"
      - "has_promotion"
      - "is_holiday"
  
  # Feature importance weights for overall drift score
  importance_weights:
    sales: 1.0
    quantity_sold: 0.8
    profit: 0.8
    customer_traffic: 0.6
    has_promotion: 0.4
    is_holiday: 0.4

# Retraining Triggers
retraining:
  # Automatic retraining triggers
  triggers:
    drift_based:
      enabled: true
      # Trigger retraining if drift severity reaches these levels
      severity_threshold: "moderate"
      # Minimum features with drift to trigger
      min_features_with_drift: 2
      # Cooldown period between retraining (hours)
      cooldown_hours: 24
    
    performance_based:
      enabled: true
      # Performance degradation thresholds
      rmse_degradation_threshold: 0.2  # 20% increase
      mape_degradation_threshold: 0.1  # 10% increase
      
    scheduled:
      enabled: true
      # Regular retraining schedule
      frequency: "weekly"
      day_of_week: "sunday"
      hour: 2

# Notification Configuration
notifications:
  # Email notifications (if configured)
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    sender_email: ""
    sender_password: ""
    recipients: []
  
  # Slack notifications (if configured)
  slack:
    enabled: false
    webhook_url: ""
    channel: "#ml-alerts"
  
  # Teams notifications (if configured)
  teams:
    enabled: false
    webhook_url: ""

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Log file settings
  file:
    enabled: true
    path: "/tmp/drift_monitoring.log"
    max_size_mb: 100
    backup_count: 5
  
  # Console logging
  console:
    enabled: true
    level: "INFO"

# Performance Configuration
performance:
  # Parallel processing
  parallel_processing:
    enabled: true
    max_workers: 4
  
  # Memory management
  memory:
    max_memory_usage_gb: 4
    chunk_size: 10000
  
  # Caching
  caching:
    enabled: true
    cache_size_mb: 500
    cache_ttl_hours: 24
