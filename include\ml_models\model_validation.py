"""
Automated Model Validation System
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib

logger = logging.getLogger(__name__)


class ModelValidator:
    """
    Comprehensive model validation system for automated retraining pipeline
    """
    
    def __init__(self, config_path: Optional[str] = None):
        self.config = self._load_config(config_path)
        self.validation_thresholds = self.config.get('validation_thresholds', {})
        
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """Load validation configuration"""
        default_config = {
            'validation_thresholds': {
                'min_r2_score': 0.7,
                'max_rmse_ratio': 1.2,  # Compared to baseline
                'min_data_points': 1000,
                'max_prediction_time_ms': 100,
                'min_feature_importance_stability': 0.8
            },
            'performance_tests': {
                'cross_validation_folds': 5,
                'bootstrap_samples': 100,
                'stability_test_iterations': 10
            },
            'business_rules': {
                'max_negative_predictions_ratio': 0.01,
                'prediction_range_multiplier': 3.0,  # Max prediction should be within 3x of historical max
                'seasonal_consistency_threshold': 0.85
            }
        }
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"Failed to load config from {config_path}: {e}")
        
        return default_config
    
    def validate_model_performance(self, model, X_test: pd.DataFrame, y_test: pd.Series,
                                 baseline_metrics: Optional[Dict] = None) -> Dict:
        """
        Validate model performance against thresholds and baseline
        """
        logger.info("Validating model performance...")
        
        validation_results = {
            'timestamp': datetime.now().isoformat(),
            'performance_metrics': {},
            'threshold_checks': {},
            'baseline_comparison': {},
            'overall_status': 'unknown'
        }
        
        try:
            # Generate predictions
            y_pred = model.predict(X_test)
            
            # Calculate performance metrics
            metrics = {
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred)),
                'mae': mean_absolute_error(y_test, y_pred),
                'r2': r2_score(y_test, y_pred),
                'mape': np.mean(np.abs((y_test - y_pred) / y_test)) * 100,
                'prediction_count': len(y_pred),
                'negative_predictions_count': int(np.sum(y_pred < 0)),
                'negative_predictions_ratio': float(np.sum(y_pred < 0) / len(y_pred))
            }
            
            validation_results['performance_metrics'] = metrics
            
            # Check against thresholds
            threshold_checks = self._check_performance_thresholds(metrics)
            validation_results['threshold_checks'] = threshold_checks
            
            # Compare with baseline if provided
            if baseline_metrics:
                baseline_comparison = self._compare_with_baseline(metrics, baseline_metrics)
                validation_results['baseline_comparison'] = baseline_comparison
            
            # Business rule validation
            business_validation = self._validate_business_rules(y_test, y_pred)
            validation_results['business_validation'] = business_validation
            
            # Determine overall status
            validation_results['overall_status'] = self._determine_validation_status(
                threshold_checks, baseline_comparison if baseline_metrics else {}, business_validation
            )
            
        except Exception as e:
            logger.error(f"Model performance validation failed: {e}")
            validation_results['error'] = str(e)
            validation_results['overall_status'] = 'failed'
        
        return validation_results
    
    def _check_performance_thresholds(self, metrics: Dict) -> Dict:
        """Check if metrics meet minimum thresholds"""
        thresholds = self.validation_thresholds
        
        checks = {
            'r2_threshold': {
                'value': metrics['r2'],
                'threshold': thresholds.get('min_r2_score', 0.7),
                'passed': metrics['r2'] >= thresholds.get('min_r2_score', 0.7)
            },
            'data_points_threshold': {
                'value': metrics['prediction_count'],
                'threshold': thresholds.get('min_data_points', 1000),
                'passed': metrics['prediction_count'] >= thresholds.get('min_data_points', 1000)
            },
            'negative_predictions_threshold': {
                'value': metrics['negative_predictions_ratio'],
                'threshold': self.config['business_rules'].get('max_negative_predictions_ratio', 0.01),
                'passed': metrics['negative_predictions_ratio'] <= self.config['business_rules'].get('max_negative_predictions_ratio', 0.01)
            }
        }
        
        checks['all_passed'] = all(check['passed'] for check in checks.values() if isinstance(check, dict))
        
        return checks
    
    def _compare_with_baseline(self, current_metrics: Dict, baseline_metrics: Dict) -> Dict:
        """Compare current model with baseline model"""
        comparison = {}
        
        for metric in ['rmse', 'mae', 'r2', 'mape']:
            if metric in current_metrics and metric in baseline_metrics:
                current_value = current_metrics[metric]
                baseline_value = baseline_metrics[metric]
                
                if metric in ['rmse', 'mae', 'mape']:  # Lower is better
                    improvement = (baseline_value - current_value) / baseline_value * 100
                    is_better = current_value < baseline_value
                else:  # Higher is better (r2)
                    improvement = (current_value - baseline_value) / baseline_value * 100
                    is_better = current_value > baseline_value
                
                comparison[metric] = {
                    'current_value': current_value,
                    'baseline_value': baseline_value,
                    'improvement_percent': improvement,
                    'is_better': is_better,
                    'significant_improvement': abs(improvement) > 5.0  # 5% threshold
                }
        
        # Overall comparison
        better_metrics = sum(1 for comp in comparison.values() if comp['is_better'])
        total_metrics = len(comparison)
        
        comparison['summary'] = {
            'better_metrics_count': better_metrics,
            'total_metrics': total_metrics,
            'improvement_ratio': better_metrics / total_metrics if total_metrics > 0 else 0,
            'overall_better': better_metrics > total_metrics / 2
        }
        
        return comparison
    
    def _validate_business_rules(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict:
        """Validate predictions against business rules"""
        business_rules = self.config.get('business_rules', {})
        
        validation = {
            'negative_predictions': {
                'count': int(np.sum(y_pred < 0)),
                'ratio': float(np.sum(y_pred < 0) / len(y_pred)),
                'threshold': business_rules.get('max_negative_predictions_ratio', 0.01),
                'passed': float(np.sum(y_pred < 0) / len(y_pred)) <= business_rules.get('max_negative_predictions_ratio', 0.01)
            },
            'prediction_range': {
                'min_prediction': float(np.min(y_pred)),
                'max_prediction': float(np.max(y_pred)),
                'historical_max': float(np.max(y_true)),
                'range_multiplier': business_rules.get('prediction_range_multiplier', 3.0),
                'passed': float(np.max(y_pred)) <= float(np.max(y_true)) * business_rules.get('prediction_range_multiplier', 3.0)
            }
        }
        
        # Check for extreme outliers
        q99 = np.percentile(y_true, 99)
        extreme_predictions = np.sum(y_pred > q99 * 2)  # Predictions > 2x 99th percentile
        
        validation['outlier_check'] = {
            'extreme_predictions_count': int(extreme_predictions),
            'extreme_predictions_ratio': float(extreme_predictions / len(y_pred)),
            'threshold': 0.05,  # 5% threshold
            'passed': float(extreme_predictions / len(y_pred)) <= 0.05
        }
        
        validation['all_passed'] = all(
            rule['passed'] for rule in validation.values() 
            if isinstance(rule, dict) and 'passed' in rule
        )
        
        return validation
    
    def _determine_validation_status(self, threshold_checks: Dict, baseline_comparison: Dict, 
                                   business_validation: Dict) -> str:
        """Determine overall validation status"""
        
        # Critical failures
        if not threshold_checks.get('all_passed', False):
            return 'failed_thresholds'
        
        if not business_validation.get('all_passed', False):
            return 'failed_business_rules'
        
        # If we have baseline comparison
        if baseline_comparison:
            if baseline_comparison.get('summary', {}).get('overall_better', False):
                improvement_ratio = baseline_comparison.get('summary', {}).get('improvement_ratio', 0)
                if improvement_ratio > 0.7:  # 70% of metrics improved
                    return 'excellent'
                elif improvement_ratio > 0.5:  # 50% of metrics improved
                    return 'good'
                else:
                    return 'marginal'
            else:
                return 'worse_than_baseline'
        
        # No baseline comparison, just check if thresholds passed
        return 'acceptable'
    
    def validate_model_stability(self, model, X_test: pd.DataFrame, y_test: pd.Series) -> Dict:
        """
        Test model stability through bootstrap sampling
        """
        logger.info("Validating model stability...")
        
        stability_config = self.config.get('performance_tests', {})
        n_samples = stability_config.get('bootstrap_samples', 100)
        
        stability_results = {
            'timestamp': datetime.now().isoformat(),
            'bootstrap_samples': n_samples,
            'metrics_stability': {},
            'overall_stability': 'unknown'
        }
        
        try:
            # Collect metrics from bootstrap samples
            bootstrap_metrics = {'rmse': [], 'mae': [], 'r2': []}
            
            for i in range(n_samples):
                # Bootstrap sample
                indices = np.random.choice(len(X_test), size=len(X_test), replace=True)
                X_bootstrap = X_test.iloc[indices]
                y_bootstrap = y_test.iloc[indices]
                
                # Predict and calculate metrics
                y_pred_bootstrap = model.predict(X_bootstrap)
                
                bootstrap_metrics['rmse'].append(np.sqrt(mean_squared_error(y_bootstrap, y_pred_bootstrap)))
                bootstrap_metrics['mae'].append(mean_absolute_error(y_bootstrap, y_pred_bootstrap))
                bootstrap_metrics['r2'].append(r2_score(y_bootstrap, y_pred_bootstrap))
            
            # Calculate stability statistics
            for metric, values in bootstrap_metrics.items():
                stability_results['metrics_stability'][metric] = {
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values)),
                    'coefficient_of_variation': float(np.std(values) / np.mean(values)) if np.mean(values) != 0 else float('inf'),
                    'min': float(np.min(values)),
                    'max': float(np.max(values)),
                    'percentile_5': float(np.percentile(values, 5)),
                    'percentile_95': float(np.percentile(values, 95))
                }
            
            # Determine overall stability
            avg_cv = np.mean([
                stability_results['metrics_stability'][metric]['coefficient_of_variation']
                for metric in bootstrap_metrics.keys()
            ])
            
            if avg_cv < 0.1:
                stability_results['overall_stability'] = 'excellent'
            elif avg_cv < 0.2:
                stability_results['overall_stability'] = 'good'
            elif avg_cv < 0.3:
                stability_results['overall_stability'] = 'acceptable'
            else:
                stability_results['overall_stability'] = 'poor'
            
            stability_results['average_coefficient_of_variation'] = float(avg_cv)
            
        except Exception as e:
            logger.error(f"Model stability validation failed: {e}")
            stability_results['error'] = str(e)
            stability_results['overall_stability'] = 'failed'
        
        return stability_results
    
    def generate_validation_report(self, performance_validation: Dict, 
                                 stability_validation: Dict) -> Dict:
        """
        Generate comprehensive validation report
        """
        report = {
            'validation_timestamp': datetime.now().isoformat(),
            'performance_validation': performance_validation,
            'stability_validation': stability_validation,
            'overall_recommendation': 'unknown',
            'confidence_score': 0.0,
            'deployment_recommendation': {}
        }
        
        # Determine overall recommendation
        performance_status = performance_validation.get('overall_status', 'unknown')
        stability_status = stability_validation.get('overall_stability', 'unknown')
        
        # Calculate confidence score
        confidence_factors = []
        
        if performance_status in ['excellent', 'good']:
            confidence_factors.append(0.4)
        elif performance_status == 'acceptable':
            confidence_factors.append(0.2)
        
        if stability_status in ['excellent', 'good']:
            confidence_factors.append(0.3)
        elif stability_status == 'acceptable':
            confidence_factors.append(0.1)
        
        # Business rules compliance
        if performance_validation.get('business_validation', {}).get('all_passed', False):
            confidence_factors.append(0.3)
        
        confidence_score = sum(confidence_factors)
        report['confidence_score'] = confidence_score
        
        # Generate recommendation
        if confidence_score >= 0.8 and performance_status in ['excellent', 'good']:
            recommendation = 'deploy_to_production'
        elif confidence_score >= 0.6 and performance_status in ['excellent', 'good', 'acceptable']:
            recommendation = 'deploy_to_staging'
        elif confidence_score >= 0.4:
            recommendation = 'deploy_with_monitoring'
        else:
            recommendation = 'reject_deployment'
        
        report['overall_recommendation'] = recommendation
        
        # Detailed deployment recommendation
        report['deployment_recommendation'] = {
            'action': recommendation,
            'confidence': 'high' if confidence_score >= 0.8 else 'medium' if confidence_score >= 0.6 else 'low',
            'monitoring_requirements': self._get_monitoring_requirements(performance_status, stability_status),
            'rollback_plan': self._get_rollback_plan(recommendation)
        }
        
        return report
    
    def _get_monitoring_requirements(self, performance_status: str, stability_status: str) -> List[str]:
        """Get monitoring requirements based on validation results"""
        requirements = ['standard_performance_monitoring']
        
        if stability_status in ['acceptable', 'poor']:
            requirements.append('enhanced_stability_monitoring')
        
        if performance_status == 'marginal':
            requirements.append('frequent_performance_checks')
        
        return requirements
    
    def _get_rollback_plan(self, recommendation: str) -> Dict:
        """Get rollback plan based on recommendation"""
        if recommendation == 'deploy_to_production':
            return {
                'trigger_conditions': ['performance_degradation_5_percent', 'stability_issues'],
                'rollback_time': '15_minutes',
                'monitoring_period': '24_hours'
            }
        elif recommendation in ['deploy_to_staging', 'deploy_with_monitoring']:
            return {
                'trigger_conditions': ['any_performance_degradation', 'stability_issues'],
                'rollback_time': '5_minutes',
                'monitoring_period': '72_hours'
            }
        else:
            return {'rollback_plan': 'not_applicable'}


# Export the main class
__all__ = ['ModelValidator']
