{"dashboard": {"id": null, "title": "Training Pipeline", "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "5s", "panels": [{"id": 1, "type": "stat", "title": "Data Records Processed", "gridPos": {"x": 0, "y": 0, "w": 6, "h": 4}, "targets": [{"expr": "increase(data_processed_records_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 2, "type": "graph", "title": "Training Jobs Over Time", "gridPos": {"x": 6, "y": 0, "w": 12, "h": 4}, "targets": [{"expr": "increase(models_trained_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 3, "type": "gauge", "title": "Training Success Rate", "gridPos": {"x": 0, "y": 4, "w": 6, "h": 4}, "targets": [{"expr": "1 - (increase(training_errors_total[1h]) / increase(models_trained_total[1h]))", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 4, "type": "table", "title": "Top Error Types", "gridPos": {"x": 6, "y": 4, "w": 12, "h": 4}, "targets": [{"expr": "topk(5, training_errors_total)", "format": "table", "intervalFactor": 1, "refId": "A"}]}]}}