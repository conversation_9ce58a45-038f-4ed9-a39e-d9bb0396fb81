"""
Automated Sales Forecast Model Retraining Pipeline
"""

from datetime import datetime, timedelta
from airflow.decorators import dag, task
from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from airflow.sensors.filesystem import FileSensor
from airflow.operators.python import BranchPythonOperator

import pandas as pd
import numpy as np
import sys
import os
import logging

# Add include path
include_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "include")
sys.path.append(include_path)

# Import custom classes
from include.ml_models.train_models import ModelTrainer
from include.utils.mlflow_utils import MLflowManager
from include.ml_models.drift_integration import DriftIntegration
from include.ml_models.drift_monitoring_service import DriftMonitoringService

logger = logging.getLogger(__name__)

default_args = {
    "owner": "sagar-roy",
    "depends_on_past": False,
    "start_date": datetime(2025, 1, 1),
    "retries": 2,
    "retry_delay": timedelta(minutes=5),
    "catchup": False,
    "email_on_failure": False,
    "email_on_retry": False,
}


@dag(
    schedule="@daily",  # Check daily for retraining triggers
    start_date=datetime(2025, 1, 1),
    catchup=False,
    default_args=default_args,
    description="Automated model retraining pipeline with drift detection and validation",
    tags=["ml", "retraining", "sales", "automated"],
    max_active_runs=1,  # Prevent concurrent retraining runs
)
def sales_forecast_retraining():
    
    @task()
    def check_retraining_triggers():
        """
        Check if retraining should be triggered based on various conditions
        """
        from include.ml_models.drift_integration import DriftIntegration
        from include.utils.data_generator import RealisticSalesDataGenerator
        
        logger.info("Checking retraining triggers...")
        
        # Generate fresh data for analysis
        data_output_dir = "/tmp/retraining_data_check"
        generator = RealisticSalesDataGenerator(
            start_date=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d"),
            end_date=datetime.now().strftime("%Y-%m-%d")
        )
        
        file_paths = generator.generate_sales_data(output_dir=data_output_dir)
        
        # Load recent data for drift analysis
        sales_dfs = []
        for sales_file in file_paths["sales"][:5]:  # Use recent 5 files
            df = pd.read_parquet(sales_file)
            sales_dfs.append(df)
        
        recent_data = pd.concat(sales_dfs, ignore_index=True)
        
        # Aggregate to daily sales
        daily_sales = (
            recent_data.groupby(["date", "store_id"])
            .agg({
                "revenue": "sum",
                "quantity_sold": "sum",
                "profit": "sum",
                "discount_percent": "mean"
            })
            .reset_index()
        )
        daily_sales = daily_sales.rename(columns={"revenue": "sales"})
        
        # Initialize drift integration
        drift_integration = DriftIntegration()
        
        # Check for drift
        drift_results = drift_integration.check_drift_before_training(daily_sales)
        should_retrain, reason = drift_integration.should_trigger_retraining(drift_results)
        
        # Check time-based trigger (weekly retraining)
        mlflow_manager = MLflowManager()
        latest_run = mlflow_manager.get_best_model(metric="rmse", ascending=True)
        
        time_based_trigger = False
        if latest_run:
            last_training_time = datetime.fromisoformat(latest_run.get('start_time', '2020-01-01'))
            days_since_training = (datetime.now() - last_training_time).days
            time_based_trigger = days_since_training >= 7  # Weekly retraining
        else:
            time_based_trigger = True  # No previous training found
        
        # Determine final trigger decision
        trigger_decision = {
            'should_retrain': should_retrain or time_based_trigger,
            'drift_based': should_retrain,
            'time_based': time_based_trigger,
            'drift_reason': reason,
            'days_since_last_training': days_since_training if latest_run else None,
            'drift_status': drift_results.get('summary', {}).get('overall_drift_status', 'unknown'),
            'data_shape': daily_sales.shape
        }
        
        logger.info(f"Retraining trigger decision: {trigger_decision}")
        
        # Cleanup temporary data
        import shutil
        if os.path.exists(data_output_dir):
            shutil.rmtree(data_output_dir)
        
        return trigger_decision
    
    @task()
    def prepare_retraining_data(trigger_info):
        """
        Prepare comprehensive dataset for retraining
        """
        if not trigger_info['should_retrain']:
            logger.info("Retraining not triggered, skipping data preparation")
            return {'status': 'skipped', 'reason': 'No retraining trigger'}
        
        logger.info("Preparing retraining dataset...")
        
        from include.utils.data_generator import RealisticSalesDataGenerator
        
        # Generate comprehensive training data (last 6 months)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=180)
        
        data_output_dir = "/tmp/retraining_data"
        generator = RealisticSalesDataGenerator(
            start_date=start_date.strftime("%Y-%m-%d"),
            end_date=end_date.strftime("%Y-%m-%d")
        )
        
        file_paths = generator.generate_sales_data(output_dir=data_output_dir)
        
        # Load and process all data
        sales_dfs = []
        for sales_file in file_paths["sales"]:
            df = pd.read_parquet(sales_file)
            sales_dfs.append(df)
        
        sales_df = pd.concat(sales_dfs, ignore_index=True)
        
        # Add external data if available
        if file_paths.get("promotions"):
            promo_df = pd.read_parquet(file_paths["promotions"][0])
            promo_summary = (
                promo_df.groupby(["date", "product_id"])["discount_percent"]
                .max()
                .reset_index()
            )
            promo_summary["has_promotion"] = 1
            
            sales_df = sales_df.merge(
                promo_summary[["date", "product_id", "has_promotion"]],
                on=["date", "product_id"],
                how="left"
            )
            sales_df["has_promotion"] = sales_df["has_promotion"].fillna(0)
        
        if file_paths.get("customer_traffic"):
            traffic_dfs = []
            for traffic_file in file_paths["customer_traffic"][:10]:
                traffic_dfs.append(pd.read_parquet(traffic_file))
            
            traffic_df = pd.concat(traffic_dfs, ignore_index=True)
            traffic_summary = (
                traffic_df.groupby(["date", "store_id"])
                .agg({"customer_traffic": "sum", "is_holiday": "max"})
                .reset_index()
            )
            
            sales_df = sales_df.merge(
                traffic_summary, on=["date", "store_id"], how="left"
            )
        
        # Aggregate to daily store-level sales
        daily_sales = (
            sales_df.groupby(["date", "store_id"])
            .agg({
                "revenue": "sum",
                "quantity_sold": "sum",
                "profit": "sum",
                "has_promotion": "mean",
                "customer_traffic": "first",
                "is_holiday": "first"
            })
            .reset_index()
        )
        daily_sales = daily_sales.rename(columns={"revenue": "sales"})
        daily_sales["date"] = pd.to_datetime(daily_sales["date"])
        
        # Save prepared data
        prepared_data_path = "/tmp/prepared_retraining_data.parquet"
        daily_sales.to_parquet(prepared_data_path)
        
        logger.info(f"Prepared retraining data: {daily_sales.shape}")
        
        return {
            'status': 'success',
            'data_path': prepared_data_path,
            'data_shape': daily_sales.shape,
            'date_range': {
                'start': daily_sales['date'].min().isoformat(),
                'end': daily_sales['date'].max().isoformat()
            },
            'features': daily_sales.columns.tolist()
        }
    
    @task()
    def validate_retraining_data(data_info):
        """
        Validate data quality before retraining
        """
        if data_info['status'] == 'skipped':
            return {'status': 'skipped', 'reason': 'Data preparation was skipped'}
        
        logger.info("Validating retraining data quality...")
        
        # Load prepared data
        data = pd.read_parquet(data_info['data_path'])
        
        # Perform comprehensive validation
        validation_results = {
            'data_shape': data.shape,
            'missing_values': data.isnull().sum().to_dict(),
            'data_types': data.dtypes.to_dict(),
            'date_range': {
                'start': data['date'].min().isoformat(),
                'end': data['date'].max().isoformat(),
                'days': (data['date'].max() - data['date'].min()).days
            },
            'sales_statistics': {
                'mean': float(data['sales'].mean()),
                'std': float(data['sales'].std()),
                'min': float(data['sales'].min()),
                'max': float(data['sales'].max()),
                'zero_sales_count': int((data['sales'] == 0).sum())
            }
        }
        
        # Quality checks
        quality_issues = []
        
        # Check for sufficient data
        if data.shape[0] < 1000:
            quality_issues.append("Insufficient data points (< 1000)")
        
        # Check for excessive missing values
        missing_threshold = 0.1
        for col, missing_count in validation_results['missing_values'].items():
            missing_ratio = missing_count / len(data)
            if missing_ratio > missing_threshold:
                quality_issues.append(f"High missing values in {col}: {missing_ratio:.2%}")
        
        # Check for data freshness
        days_old = (datetime.now() - data['date'].max()).days
        if days_old > 7:
            quality_issues.append(f"Data is {days_old} days old")
        
        # Check for negative sales
        if (data['sales'] < 0).any():
            quality_issues.append("Negative sales values detected")
        
        # Calculate quality score
        quality_score = max(0, 1 - len(quality_issues) * 0.2)
        
        validation_results.update({
            'quality_issues': quality_issues,
            'quality_score': quality_score,
            'validation_passed': quality_score >= 0.7
        })
        
        logger.info(f"Data validation completed. Quality score: {quality_score:.2f}")
        if quality_issues:
            logger.warning(f"Quality issues found: {quality_issues}")
        
        return validation_results

    @task()
    def retrain_models(data_info, validation_info):
        """
        Retrain models with new data and hyperparameter optimization
        """
        if data_info['status'] == 'skipped':
            return {'status': 'skipped', 'reason': 'Data preparation was skipped'}

        if not validation_info['validation_passed']:
            return {
                'status': 'failed',
                'reason': f"Data validation failed: {validation_info['quality_issues']}"
            }

        logger.info("Starting model retraining...")

        # Load validated data
        data = pd.read_parquet(data_info['data_path'])

        # Initialize trainer with enhanced configuration
        trainer = ModelTrainer()

        # Prepare data splits
        train_df, val_df, test_df = trainer.prepare_data(
            data,
            target_col="sales",
            date_col="date",
            group_cols=["store_id"],
            categorical_cols=["store_id"],
        )

        logger.info(f"Data splits - Train: {train_df.shape}, Val: {val_df.shape}, Test: {test_df.shape}")

        # Train all models with hyperparameter optimization
        training_results = trainer.train_all_models(
            train_df, val_df, test_df,
            target_col='sales',
            use_optuna=True
        )

        # Extract key metrics for comparison
        model_metrics = {}
        for model_name, results in training_results.items():
            if 'metrics' in results:
                model_metrics[model_name] = {
                    'rmse': results['metrics'].get('rmse', float('inf')),
                    'mae': results['metrics'].get('mae', float('inf')),
                    'r2': results['metrics'].get('r2', 0),
                    'mape': results['metrics'].get('mape', float('inf'))
                }

        # Determine best model
        best_model = min(model_metrics.keys(),
                        key=lambda x: model_metrics[x]['rmse']) if model_metrics else None

        retraining_summary = {
            'status': 'success',
            'models_trained': list(training_results.keys()),
            'best_model': best_model,
            'model_metrics': model_metrics,
            'training_data_shape': data.shape,
            'mlflow_run_id': training_results.get('mlflow_run_id'),
            'training_timestamp': datetime.now().isoformat()
        }

        logger.info(f"Retraining completed. Best model: {best_model}")

        return retraining_summary

    @task()
    def evaluate_model_performance(training_results, validation_info):
        """
        Comprehensive evaluation of retrained models
        """
        if training_results['status'] != 'success':
            return {'status': 'skipped', 'reason': 'Training was not successful'}

        logger.info("Evaluating model performance...")

        # Load test data for evaluation
        data = pd.read_parquet("/tmp/prepared_retraining_data.parquet")

        # Get current production model for comparison
        mlflow_manager = MLflowManager()
        current_production_model = mlflow_manager.get_best_model(metric="rmse", ascending=True)

        evaluation_results = {
            'new_model_metrics': training_results['model_metrics'],
            'best_new_model': training_results['best_model'],
            'evaluation_timestamp': datetime.now().isoformat()
        }

        # Compare with production model if available
        if current_production_model and training_results['best_model']:
            best_model_name = training_results['best_model']
            new_model_rmse = training_results['model_metrics'][best_model_name]['rmse']

            # Get production model metrics (simplified comparison)
            production_rmse = current_production_model.get('rmse', float('inf'))

            improvement = (production_rmse - new_model_rmse) / production_rmse * 100

            evaluation_results.update({
                'production_model_rmse': production_rmse,
                'new_model_rmse': new_model_rmse,
                'improvement_percentage': improvement,
                'is_better': improvement > 0,
                'significant_improvement': improvement > 5.0  # 5% improvement threshold
            })

            logger.info(f"Model comparison - Improvement: {improvement:.2f}%")
        else:
            evaluation_results.update({
                'production_model_rmse': None,
                'improvement_percentage': None,
                'is_better': True,  # No production model to compare
                'significant_improvement': True
            })

        # Additional evaluation metrics
        evaluation_results.update({
            'model_stability': self._assess_model_stability(training_results),
            'feature_importance_analysis': self._analyze_feature_importance(training_results),
            'recommendation': self._generate_deployment_recommendation(evaluation_results)
        })

        return evaluation_results

    def _assess_model_stability(self, training_results):
        """Assess model stability across different metrics"""
        if not training_results.get('model_metrics'):
            return {'status': 'unknown', 'reason': 'No metrics available'}

        # Calculate coefficient of variation across models
        rmse_values = [metrics['rmse'] for metrics in training_results['model_metrics'].values()]
        r2_values = [metrics['r2'] for metrics in training_results['model_metrics'].values()]

        rmse_cv = np.std(rmse_values) / np.mean(rmse_values) if rmse_values else 0
        r2_cv = np.std(r2_values) / np.mean(r2_values) if r2_values else 0

        stability_score = 1 - min(rmse_cv, 1.0)  # Lower CV = higher stability

        return {
            'stability_score': stability_score,
            'rmse_coefficient_variation': rmse_cv,
            'r2_coefficient_variation': r2_cv,
            'is_stable': stability_score > 0.7
        }

    def _analyze_feature_importance(self, training_results):
        """Analyze feature importance (simplified version)"""
        # This would typically involve loading the actual models and analyzing feature importance
        # For now, return a placeholder analysis
        return {
            'top_features': ['date_features', 'store_id', 'has_promotion', 'customer_traffic'],
            'feature_stability': 'stable',
            'new_important_features': []
        }

    def _generate_deployment_recommendation(self, evaluation_results):
        """Generate deployment recommendation based on evaluation"""
        if evaluation_results.get('significant_improvement', False):
            return {
                'action': 'deploy',
                'confidence': 'high',
                'reason': 'Significant improvement over production model'
            }
        elif evaluation_results.get('is_better', False):
            return {
                'action': 'deploy_with_caution',
                'confidence': 'medium',
                'reason': 'Marginal improvement, monitor closely'
            }
        else:
            return {
                'action': 'reject',
                'confidence': 'high',
                'reason': 'No improvement over production model'
            }

    @task()
    def register_and_deploy_model(training_results, evaluation_results):
        """
        Register model and deploy to production if evaluation passes
        """
        if training_results['status'] != 'success':
            return {'status': 'skipped', 'reason': 'Training was not successful'}

        recommendation = evaluation_results.get('recommendation', {})

        if recommendation.get('action') == 'reject':
            logger.info("Model deployment rejected based on evaluation")
            return {
                'status': 'rejected',
                'reason': recommendation.get('reason', 'Evaluation failed'),
                'action_taken': 'none'
            }

        logger.info("Registering and deploying model...")

        mlflow_manager = MLflowManager()
        run_id = training_results.get('mlflow_run_id')
        best_model = training_results.get('best_model')

        if not run_id or not best_model:
            return {
                'status': 'failed',
                'reason': 'Missing run_id or best_model information'
            }

        # Register the best model
        try:
            model_version = mlflow_manager.register_model(
                run_id=run_id,
                model_name=best_model,
                artifact_path=f"models/{best_model}"
            )

            # Transition to production if high confidence
            if recommendation.get('confidence') == 'high':
                mlflow_manager.transition_model_stage(
                    model_name=best_model,
                    version=model_version,
                    stage="Production"
                )
                deployment_stage = "Production"
            else:
                mlflow_manager.transition_model_stage(
                    model_name=best_model,
                    version=model_version,
                    stage="Staging"
                )
                deployment_stage = "Staging"

            deployment_results = {
                'status': 'success',
                'model_name': best_model,
                'model_version': model_version,
                'deployment_stage': deployment_stage,
                'run_id': run_id,
                'deployment_timestamp': datetime.now().isoformat(),
                'recommendation': recommendation
            }

            logger.info(f"Model deployed successfully: {best_model} v{model_version} to {deployment_stage}")

        except Exception as e:
            logger.error(f"Model deployment failed: {e}")
            deployment_results = {
                'status': 'failed',
                'reason': str(e),
                'model_name': best_model,
                'run_id': run_id
            }

        return deployment_results

    @task()
    def update_drift_monitoring(deployment_results, data_info):
        """
        Update drift monitoring with new reference data
        """
        if deployment_results['status'] != 'success':
            logger.info("Skipping drift monitoring update - deployment was not successful")
            return {'status': 'skipped', 'reason': 'Deployment was not successful'}

        logger.info("Updating drift monitoring with new reference data...")

        try:
            # Load the training data as new reference
            reference_data = pd.read_parquet(data_info['data_path'])

            # Initialize drift monitoring service
            drift_service = DriftMonitoringService()

            # Update reference data in MLflow
            run_id = deployment_results['run_id']
            drift_service.update_reference_data(reference_data, run_id)

            # Reset drift monitoring baselines
            drift_integration = DriftIntegration()
            drift_integration.setup_drift_monitoring_for_training(reference_data, run_id)

            monitoring_update = {
                'status': 'success',
                'reference_data_updated': True,
                'reference_data_shape': reference_data.shape,
                'run_id': run_id,
                'update_timestamp': datetime.now().isoformat()
            }

            logger.info("Drift monitoring updated successfully")

        except Exception as e:
            logger.error(f"Failed to update drift monitoring: {e}")
            monitoring_update = {
                'status': 'failed',
                'reason': str(e),
                'reference_data_updated': False
            }

        return monitoring_update

    @task()
    def send_retraining_notification(trigger_info, training_results, evaluation_results, deployment_results):
        """
        Send notification about retraining results
        """
        logger.info("Sending retraining notification...")

        # Prepare notification content
        notification_data = {
            'retraining_triggered': trigger_info['should_retrain'],
            'trigger_reason': trigger_info.get('drift_reason', 'Time-based trigger'),
            'training_status': training_results.get('status', 'unknown'),
            'deployment_status': deployment_results.get('status', 'unknown'),
            'best_model': training_results.get('best_model'),
            'improvement': evaluation_results.get('improvement_percentage'),
            'deployment_stage': deployment_results.get('deployment_stage'),
            'timestamp': datetime.now().isoformat()
        }

        # Log notification (in production, this would send emails/Slack messages)
        logger.info(f"RETRAINING NOTIFICATION: {notification_data}")

        # In a real implementation, you would integrate with:
        # - Email service (SMTP)
        # - Slack/Teams webhooks
        # - PagerDuty for critical issues
        # - Dashboard updates

        return {
            'status': 'success',
            'notification_sent': True,
            'notification_data': notification_data
        }

    @task()
    def cleanup_retraining_artifacts():
        """
        Clean up temporary files and artifacts
        """
        logger.info("Cleaning up retraining artifacts...")

        cleanup_paths = [
            "/tmp/retraining_data_check",
            "/tmp/retraining_data",
            "/tmp/prepared_retraining_data.parquet"
        ]

        cleaned_files = []
        for path in cleanup_paths:
            try:
                if os.path.exists(path):
                    if os.path.isdir(path):
                        import shutil
                        shutil.rmtree(path)
                    else:
                        os.remove(path)
                    cleaned_files.append(path)
                    logger.info(f"Cleaned up: {path}")
            except Exception as e:
                logger.warning(f"Failed to clean up {path}: {e}")

        return {
            'status': 'success',
            'cleaned_files': cleaned_files,
            'cleanup_timestamp': datetime.now().isoformat()
        }

    # Define task dependencies
    trigger_check = check_retraining_triggers()
    data_prep = prepare_retraining_data(trigger_check)
    data_validation = validate_retraining_data(data_prep)
    model_training = retrain_models(data_prep, data_validation)
    model_evaluation = evaluate_model_performance(model_training, data_validation)
    model_deployment = register_and_deploy_model(model_training, model_evaluation)
    drift_update = update_drift_monitoring(model_deployment, data_prep)
    notification = send_retraining_notification(trigger_check, model_training, model_evaluation, model_deployment)
    cleanup = cleanup_retraining_artifacts()

    # Set up task flow
    trigger_check >> data_prep >> data_validation >> model_training >> model_evaluation >> model_deployment
    model_deployment >> [drift_update, notification] >> cleanup


# Register the DAG
sales_forecast_retraining_dag = sales_forecast_retraining()
