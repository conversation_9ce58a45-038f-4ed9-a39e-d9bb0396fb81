# Grafana Dashboarding Implementation Plan

## Overview
This document outlines the implementation plan for adding Grafana dashboarding to visualize metrics collected by Prometheus in the sales forecasting project.

## Implementation Steps

### Step 1: Add Grafana Service
- Add Grafana service to [docker-compose.override.yml](file://e:\Codecademy\Sales_prediction\docker-compose.override.yml)
- Configure data source connection to Prometheus
- Set up persistent storage for dashboards

### Step 2: Configure Data Sources
- Set up Prometheus as primary data source
- Configure any additional data sources if needed

### Step 3: Create Dashboards
- Create model performance dashboard
- Create training pipeline dashboard
- Create prediction serving dashboard
- Create data quality dashboard

### Step 4: Configure Provisioning
- Set up automatic dashboard provisioning
- Configure user management
- Set up alerting channels

## Detailed Task Breakdown

### Task 1: Add Grafana Service
**Estimated Time**: 45 minutes

Subtasks:
1. Add Grafana service to [docker-compose.override.yml](file://e:\Codecademy\Sales_prediction\docker-compose.override.yml)
2. Configure port mapping (3000:3000)
3. Set up volume mounting for persistent storage
4. Configure environment variables
5. Test service startup

### Task 2: Configure Data Sources
**Estimated Time**: 30 minutes

Subtasks:
1. Create Grafana provisioning configuration
2. Set up Prometheus data source
3. Test data source connection
4. Configure data source permissions

### Task 3: Create Dashboards
**Estimated Time**: 4 hours

Subtasks:
1. Create model performance dashboard
2. Create training pipeline dashboard
3. Create prediction serving dashboard
4. Create data quality dashboard
5. Test dashboard functionality

### Task 4: Configure Provisioning
**Estimated Time**: 1 hour

Subtasks:
1. Set up automatic dashboard provisioning
2. Configure user authentication
3. Set up alerting channels
4. Test provisioning

## Implementation Details

### Directory Structure
```
grafana/
├── dashboards/                 # Dashboard JSON files
│   ├── model-performance.json
│   ├── training-pipeline.json
│   ├── prediction-serving.json
│   └── data-quality.json
├── provisioning/              # Provisioning configurations
│   ├── dashboards/
│   │   └── default.yaml
│   └── datasources/
│       └── default.yaml
└── grafana.ini               # Grafana configuration
```

### Docker Compose Configuration

#### grafana service in docker-compose.override.yml
```yaml
  grafana:
    image: grafana/grafana-enterprise
    platform: linux/arm64
    ports:
      - "3000:3000"
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_DOMAIN=localhost
      - GF_SERVER_ROOT_URL=%(protocol)s://%(domain)s:%(http_port)s/grafana/
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - airflow
      - default
```

### Provisioning Configuration

#### datasources/default.yaml
```yaml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
```

#### dashboards/default.yaml
```yaml
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    editable: true
    options:
      path: /var/lib/grafana/dashboards
```

### Dashboard Designs

#### 1. Model Performance Dashboard
**Purpose**: Monitor trained model performance metrics

**Panels**:
- Model Accuracy Score (Gauge)
- Training Duration (Histogram)
- Model Versions Trained (Counter)
- Training Errors (Counter)
- Accuracy Trend Over Time (Graph)

**Queries**:
- `model_accuracy_score`
- `training_duration_seconds`
- `increase(model_versions_trained_total[1h])`
- `increase(training_errors_total[1h])`

#### 2. Training Pipeline Dashboard
**Purpose**: Monitor ML training pipeline health and performance

**Panels**:
- Data Records Processed (Counter)
- Training Jobs Status (Stat)
- Training Duration Distribution (Histogram)
- Training Success Rate (Gauge)
- Top Error Types (Table)

**Queries**:
- `increase(data_processed_records_total[1h])`
- `training_status{status="running"}`
- `training_duration_seconds`
- `1 - (increase(training_errors_total[1h]) / increase(model_versions_trained_total[1h]))`
- `topk(5, training_errors_total)`

#### 3. Prediction Serving Dashboard
**Purpose**: Monitor prediction serving performance and reliability

**Panels**:
- Predictions Served (Counter)
- Prediction Latency (Histogram)
- API Request Rate (Counter)
- Error Rate (Gauge)
- Models Loaded (Gauge)

**Queries**:
- `increase(predictions_served_total[1h])`
- `prediction_latency_seconds`
- `increase(api_requests_total[1h])`
- `increase(prediction_errors_total[1h]) / increase(predictions_served_total[1h])`
- `models_loaded`

#### 4. Data Quality Dashboard
**Purpose**: Monitor data quality metrics

**Panels**:
- Data Quality Score (Gauge)
- Missing Values Detected (Counter)
- Outliers Detected (Counter)
- Data Validation Failures (Counter)
- Quality Score Trend (Graph)

**Queries**:
- `data_quality_score`
- `increase(missing_values_total[1h])`
- `increase(outliers_detected_total[1h])`
- `increase(data_validation_failures_total[1h])`
- `data_quality_score`

### Dashboard JSON Examples

#### Model Performance Dashboard (model-performance.json)
```json
{
  "dashboard": {
    "id": null,
    "title": "Model Performance",
    "timezone": "browser",
    "schemaVersion": 16,
    "version": 0,
    "refresh": "5s",
    "panels": [
      {
        "id": 1,
        "type": "gauge",
        "title": "Current Model Accuracy",
        "gridPos": {
          "x": 0,
          "y": 0,
          "w": 6,
          "h": 6
        },
        "targets": [
          {
            "expr": "model_accuracy_score",
            "format": "time_series",
            "intervalFactor": 1,
            "refId": "A"
          }
        ],
        "options": {
          "fieldOptions": {
            "calcs": ["lastNotNull"]
          }
        }
      },
      {
        "id": 2,
        "type": "graph",
        "title": "Training Duration",
        "gridPos": {
          "x": 6,
          "y": 0,
          "w": 12,
          "h": 6
        },
        "targets": [
          {
            "expr": "rate(training_duration_seconds_sum[5m]) / rate(training_duration_seconds_count[5m])",
            "format": "time_series",
            "intervalFactor": 1,
            "refId": "A"
          }
        ]
      }
    ]
  }
}
```

## Testing Plan

### Unit Tests
- Test dashboard JSON validity
- Test provisioning configuration syntax
- Test data source connection

### Integration Tests
- Test Grafana service startup
- Verify dashboard provisioning
- Test data source connectivity

### Manual Testing
- Access Grafana UI at http://localhost:3000
- Verify dashboards are loaded
- Check data visualization
- Test alerting functionality

## Success Criteria

1. Grafana server starts successfully
2. Prometheus data source is configured
3. All dashboards are loaded
4. Metrics are visualized correctly
5. Grafana UI is accessible at http://localhost:3000

## Dependencies

- Docker & Docker Compose
- Prometheus server (already configured)
- Python 3.8+
- Existing project services

## Timeline Estimate

| Task | Estimated Time |
|------|----------------|
| Add Grafana Service | 45 minutes |
| Configure Data Sources | 30 minutes |
| Create Dashboards | 4 hours |
| Configure Provisioning | 1 hour |
| Testing | 1 hour |
| **Total** | **7 hours** |