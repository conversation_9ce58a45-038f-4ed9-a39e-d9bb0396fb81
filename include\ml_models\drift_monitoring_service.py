"""
Drift Monitoring Service for Production ML Systems
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
from prometheus_client import start_http_server, CollectorRegistry, CONTENT_TYPE_LATEST, generate_latest
import mlflow
from mlflow.tracking import <PERSON><PERSON><PERSON><PERSON><PERSON>

from .drift_detection import DriftDetector
from ..utils.mlflow_utils import MLflowManager

logger = logging.getLogger(__name__)


class DriftMonitoringService:
    """
    Production-ready drift monitoring service that integrates with MLflow and Prometheus
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize drift monitoring service
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path
        self.mlflow_manager = MLflowManager()
        self.mlflow_client = MlflowClient()
        
        # Load configuration
        self.config = self._load_config()
        
        # Initialize drift detector (will be set when reference data is loaded)
        self.drift_detector = None
        self.reference_data = None
        self.reference_run_id = None
        
        # Monitoring state
        self.monitoring_active = False
        self.last_check_time = None
        self.drift_history = []
        
        # Prometheus server
        self.prometheus_port = self.config.get('prometheus_port', 8000)
        self.prometheus_server = None
    
    def _load_config(self) -> Dict:
        """Load configuration with defaults"""
        default_config = {
            'monitoring': {
                'enabled': True,
                'check_interval_minutes': 60,
                'reference_data_update_hours': 24,
                'max_history_days': 30
            },
            'drift_thresholds': {
                'feature_drift': 0.05,
                'prediction_drift': 0.05,
                'concept_drift': 0.05,
                'psi_minor': 0.1,
                'psi_moderate': 0.2,
                'psi_severe': 0.5
            },
            'alerting': {
                'enabled': True,
                'channels': ['prometheus', 'log', 'mlflow'],
                'severity_levels': ['minor', 'moderate', 'severe'],
                'cooldown_minutes': 60
            },
            'prometheus_port': 8000,
            'mlflow_experiment': 'drift_monitoring'
        }
        
        if self.config_path and os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    user_config = json.load(f)
                    # Merge with defaults
                    for key, value in user_config.items():
                        if isinstance(value, dict) and key in default_config:
                            default_config[key].update(value)
                        else:
                            default_config[key] = value
            except Exception as e:
                logger.warning(f"Failed to load config from {self.config_path}: {e}")
        
        return default_config
    
    def initialize_reference_data(self, run_id: Optional[str] = None) -> bool:
        """
        Initialize reference data from MLflow run
        
        Args:
            run_id: MLflow run ID to use as reference (uses latest if None)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if run_id is None:
                # Get latest successful run
                best_run = self.mlflow_manager.get_best_model(metric="rmse", ascending=True)
                if not best_run:
                    logger.error("No successful runs found in MLflow")
                    return False
                run_id = best_run['run_id']
            
            # Download reference data from MLflow
            run = self.mlflow_client.get_run(run_id)
            artifacts = self.mlflow_client.list_artifacts(run_id)
            
            # Look for training data artifact
            training_data_path = None
            for artifact in artifacts:
                if 'training_data' in artifact.path or 'reference_data' in artifact.path:
                    training_data_path = artifact.path
                    break
            
            if training_data_path:
                # Download and load reference data
                local_path = self.mlflow_client.download_artifacts(run_id, training_data_path)
                self.reference_data = pd.read_parquet(local_path)
                self.reference_run_id = run_id
                
                # Initialize drift detector
                self.drift_detector = DriftDetector(
                    self.reference_data, 
                    config_path=self.config_path
                )
                
                logger.info(f"Reference data initialized from run {run_id}")
                logger.info(f"Reference data shape: {self.reference_data.shape}")
                return True
            else:
                logger.error(f"No training data found in run {run_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize reference data: {e}")
            return False
    
    def start_monitoring(self) -> bool:
        """
        Start drift monitoring service
        
        Returns:
            True if started successfully, False otherwise
        """
        if not self.drift_detector:
            logger.error("Drift detector not initialized. Call initialize_reference_data() first.")
            return False
        
        try:
            # Start Prometheus metrics server
            if self.prometheus_server is None:
                self.prometheus_server = start_http_server(self.prometheus_port)
                logger.info(f"Prometheus metrics server started on port {self.prometheus_port}")
            
            self.monitoring_active = True
            self.last_check_time = datetime.now()
            
            logger.info("Drift monitoring service started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start monitoring service: {e}")
            return False
    
    def stop_monitoring(self) -> None:
        """Stop drift monitoring service"""
        self.monitoring_active = False
        if self.prometheus_server:
            self.prometheus_server.shutdown()
            self.prometheus_server = None
        logger.info("Drift monitoring service stopped")
    
    def check_drift(self, current_data: pd.DataFrame, 
                   current_predictions: Optional[np.array] = None,
                   current_targets: Optional[np.array] = None,
                   model_name: str = 'unknown') -> Dict:
        """
        Perform drift check on current data
        
        Args:
            current_data: Current dataset to check
            current_predictions: Current model predictions (optional)
            current_targets: Current target values (optional)
            model_name: Name of the model being monitored
            
        Returns:
            Drift analysis results
        """
        if not self.drift_detector:
            raise ValueError("Drift detector not initialized")
        
        logger.info(f"Performing drift check for model: {model_name}")
        
        # Run comprehensive drift analysis
        drift_results = self.drift_detector.run_comprehensive_drift_analysis(
            current_data, current_predictions, current_targets, model_name
        )
        
        # Add metadata
        drift_results['metadata'] = {
            'check_timestamp': datetime.now().isoformat(),
            'model_name': model_name,
            'reference_run_id': self.reference_run_id,
            'current_data_shape': current_data.shape,
            'monitoring_service_version': '1.0.0'
        }
        
        # Store in history
        self.drift_history.append(drift_results)
        
        # Clean old history
        cutoff_date = datetime.now() - timedelta(days=self.config['monitoring']['max_history_days'])
        self.drift_history = [
            result for result in self.drift_history
            if datetime.fromisoformat(result['timestamp']) > cutoff_date
        ]
        
        # Log to MLflow if configured
        if 'mlflow' in self.config['alerting']['channels']:
            self._log_drift_to_mlflow(drift_results, model_name)
        
        self.last_check_time = datetime.now()
        
        return drift_results
    
    def _log_drift_to_mlflow(self, drift_results: Dict, model_name: str) -> None:
        """Log drift results to MLflow"""
        try:
            # Set experiment
            experiment_name = self.config.get('mlflow_experiment', 'drift_monitoring')
            mlflow.set_experiment(experiment_name)
            
            with mlflow.start_run(run_name=f"drift_check_{model_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"):
                # Log summary metrics
                summary = drift_results['summary']
                mlflow.log_metrics({
                    'total_features': summary['total_features'],
                    'features_with_drift': summary['features_with_drift'],
                    'severe_feature_drift': summary['severe_feature_drift'],
                    'features_with_psi_drift': summary['features_with_psi_drift'],
                    'prediction_drift_detected': int(summary['prediction_drift_detected']),
                    'concept_drift_detected': int(summary['concept_drift_detected'])
                })
                
                # Log overall status
                mlflow.log_param('overall_drift_status', summary['overall_drift_status'])
                mlflow.log_param('model_name', model_name)
                mlflow.log_param('reference_run_id', self.reference_run_id)
                
                # Log detailed results as artifact
                drift_file = f"drift_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(drift_file, 'w') as f:
                    json.dump(drift_results, f, indent=2, default=str)
                mlflow.log_artifact(drift_file)
                os.remove(drift_file)
                
        except Exception as e:
            logger.error(f"Failed to log drift results to MLflow: {e}")
    
    def get_monitoring_status(self) -> Dict:
        """Get current monitoring status"""
        return {
            'monitoring_active': self.monitoring_active,
            'last_check_time': self.last_check_time.isoformat() if self.last_check_time else None,
            'reference_run_id': self.reference_run_id,
            'reference_data_shape': self.reference_data.shape if self.reference_data is not None else None,
            'drift_history_count': len(self.drift_history),
            'prometheus_port': self.prometheus_port,
            'config': self.config
        }
    
    def get_drift_history(self, days: int = 7) -> List[Dict]:
        """Get drift history for specified number of days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return [
            result for result in self.drift_history
            if datetime.fromisoformat(result['timestamp']) > cutoff_date
        ]
