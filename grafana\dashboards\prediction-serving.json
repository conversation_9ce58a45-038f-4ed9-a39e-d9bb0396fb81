{"dashboard": {"id": null, "title": "Prediction Serving", "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "5s", "panels": [{"id": 1, "type": "stat", "title": "Predictions Served", "gridPos": {"x": 0, "y": 0, "w": 6, "h": 4}, "targets": [{"expr": "increase(predictions_served_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 2, "type": "graph", "title": "Prediction Latency", "gridPos": {"x": 6, "y": 0, "w": 12, "h": 4}, "targets": [{"expr": "rate(prediction_latency_seconds_sum[5m]) / rate(prediction_latency_seconds_count[5m])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 3, "type": "gauge", "title": "Prediction Error Rate", "gridPos": {"x": 0, "y": 4, "w": 6, "h": 4}, "targets": [{"expr": "increase(prediction_errors_total[1h]) / increase(predictions_served_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 4, "type": "stat", "title": "Models Loaded", "gridPos": {"x": 6, "y": 4, "w": 6, "h": 4}, "targets": [{"expr": "models_loaded", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}]}}