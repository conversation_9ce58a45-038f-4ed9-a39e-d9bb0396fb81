# Prometheus Monitoring Implementation Plan

## Overview
This document outlines the implementation plan for adding Prometheus monitoring to the sales forecasting project.

## Implementation Steps

### Step 1: Add Prometheus Client Library
- Add `prometheus-client` to requirements.txt
- Install in all relevant services (FastAPI, Streamlit, Airflow tasks)

### Step 2: Instrument ML Training Pipeline
- Add training duration metrics to [include/ml_models/train_models.py](file://e:\Codecademy\Sales_prediction\include\ml_models\train_models.py)
- Add model performance metrics
- Add data processing metrics

### Step 3: Instrument Prediction Serving
- Add prediction serving metrics to UI components
- Add latency metrics
- Add error rate metrics

### Step 4: Configure Prometheus Server
- Add Prometheus service to [docker-compose.override.yml](file://e:\Codecademy\Sales_prediction\docker-compose.override.yml)
- Create Prometheus configuration file
- Set up data scraping from all services

## Detailed Task Breakdown

### Task 1: Add Prometheus Client Library
**Estimated Time**: 30 minutes

Subtasks:
1. Add `prometheus-client` to main [requirements.txt](file://e:\Codecademy\Sales_prediction\requirements.txt)
2. Add to UI [requirements.txt](file://e:\Codecademy\Sales_prediction\ui\requirements.txt) if needed
3. Verify compatibility with existing dependencies

### Task 2: Instrument ML Training Pipeline
**Estimated Time**: 2 hours

Subtasks:
1. Import prometheus-client in [include/ml_models/train_models.py](file://e:\Codecademy\Sales_prediction\include\ml_models\train_models.py)
2. Add training duration histogram
3. Add model accuracy gauge
4. Add data processing counters
5. Add error counters

### Task 3: Instrument Prediction Serving
**Estimated Time**: 2 hours

Subtasks:
1. Import prometheus-client in UI components
2. Add prediction latency histogram
3. Add prediction count counter
4. Add error rate counter
5. Add model loading status gauge

### Task 4: Configure Prometheus Server
**Estimated Time**: 1 hour

Subtasks:
1. Add Prometheus service to [docker-compose.override.yml](file://e:\Codecademy\Sales_prediction\docker-compose.override.yml)
2. Create [prometheus/prometheus.yml](file://e:\Codecademy\Sales_prediction\prometheus\prometheus.yml) configuration file
3. Configure targets for all services
4. Set up persistent storage
5. Configure port mapping (9090:9090)

## Implementation Details

### Metrics to Collect

#### Training Pipeline Metrics
- `training_duration_seconds` (Histogram): Time taken to train models
- `model_accuracy_score` (Gauge): Accuracy of trained models
- `data_processed_records_total` (Counter): Number of records processed
- `training_errors_total` (Counter): Number of training errors
- `model_versions_trained_total` (Counter): Number of model versions trained

#### Prediction Serving Metrics
- `prediction_latency_seconds` (Histogram): Time taken to serve predictions
- `predictions_served_total` (Counter): Total predictions served
- `prediction_errors_total` (Counter): Number of prediction errors
- `models_loaded` (Gauge): Number of models currently loaded
- `api_requests_total` (Counter): Total API requests by endpoint

#### Data Quality Metrics
- `data_quality_score` (Gauge): Data quality score
- `missing_values_total` (Counter): Total missing values detected
- `outliers_detected_total` (Counter): Total outliers detected

### Code Examples

#### Training Pipeline Instrumentation
```python
# In include/ml_models/train_models.py
from prometheus_client import Counter, Histogram, Gauge

# Define metrics
training_duration = Histogram('training_duration_seconds', 'Time spent training models')
model_accuracy = Gauge('model_accuracy_score', 'Model accuracy score')
data_records_processed = Counter('data_processed_records_total', 'Total records processed')
training_errors = Counter('training_errors_total', 'Total training errors')

# In training function
def train_all_models(self, train_df, val_df, test_df, target_col='sales', use_optuna=True):
    start_time = time.time()
    
    try:
        # Training logic here
        results = self._train_models_with_validation(train_df, val_df, test_df, target_col, use_optuna)
        
        # Record metrics
        training_duration.observe(time.time() - start_time)
        model_accuracy.set(results['best_model']['metrics']['r2'])  # Example
        
        return results
    except Exception as e:
        training_errors.inc()
        raise
```

#### Prediction Serving Instrumentation
```python
# In UI prediction components
from prometheus_client import Counter, Histogram, Gauge

# Define metrics
prediction_latency = Histogram('prediction_latency_seconds', 'Prediction serving latency')
predictions_served = Counter('predictions_served_total', 'Total predictions served')
prediction_errors = Counter('prediction_errors_total', 'Total prediction errors')

# In prediction function
def predict(self, input_data, model_type="ensemble", forecast_days=30):
    start_time = time.time()
    
    try:
        # Prediction logic here
        results = self._make_predictions(input_data, model_type, forecast_days)
        
        # Record metrics
        prediction_latency.observe(time.time() - start_time)
        predictions_served.inc(len(results['predictions']))
        
        return results
    except Exception as e:
        prediction_errors.inc()
        raise
```

### Prometheus Configuration

#### docker-compose.override.yml Addition
```yaml
  prometheus:
    image: prom/prometheus:latest
    platform: linux/arm64
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - airflow
      - default
```

#### prometheus.yml Configuration
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'fastapi'
    static_configs:
      - targets: ['fastapi:8000']

  - job_name: 'streamlit-ui'
    static_configs:
      - targets: ['streamlit-ui:8501']

  - job_name: 'mlflow'
    static_configs:
      - targets: ['mlflow:5001']

  - job_name: 'airflow-webserver'
    static_configs:
      - targets: ['airflow-webserver:8080']

  - job_name: 'airflow-scheduler'
    static_configs:
      - targets: ['airflow-scheduler:8080']
```

## Testing Plan

### Unit Tests
- Test metric collection in training pipeline
- Test metric collection in prediction serving
- Verify metric types and labels

### Integration Tests
- Test Prometheus server startup
- Verify metric scraping from all services
- Test data persistence

### Manual Testing
- Access Prometheus UI at http://localhost:9090
- Verify targets are up
- Check metric data is being collected
- Test query functionality

## Success Criteria

1. Prometheus server starts successfully
2. All services are scraped for metrics
3. Metrics are collected and stored
4. Prometheus UI is accessible
5. Metrics are properly labeled and typed

## Dependencies

- Docker & Docker Compose
- Python 3.8+
- Existing project services
- prometheus-client library

## Timeline Estimate

| Task | Estimated Time |
|------|----------------|
| Add Prometheus Client | 30 minutes |
| Instrument Training Pipeline | 2 hours |
| Instrument Prediction Serving | 2 hours |
| Configure Prometheus Server | 1 hour |
| Testing | 1 hour |
| **Total** | **6.5 hours** |