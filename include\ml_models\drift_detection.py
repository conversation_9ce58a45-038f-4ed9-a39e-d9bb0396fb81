"""
Enhanced Model Drift Detection Module with Monitoring and Alerting
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Optional, Any
import logging
import json
import os
from datetime import datetime, timedelta
from prometheus_client import Gauge, Counter, Histogram, CollectorRegistry, push_to_gateway
import yaml

logger = logging.getLogger(__name__)

# Prometheus metrics
DRIFT_METRICS_REGISTRY = CollectorRegistry()

# Feature drift metrics
feature_drift_detected = Gauge(
    'feature_drift_detected',
    'Number of features with drift detected',
    ['feature_name', 'severity'],
    registry=DRIFT_METRICS_REGISTRY
)

psi_drift_score = Gauge(
    'psi_drift_score',
    'Population Stability Index score for features',
    ['feature_name'],
    registry=DRIFT_METRICS_REGISTRY
)

prediction_drift_detected = Gauge(
    'prediction_drift_detected',
    'Prediction drift detection status',
    ['model_name', 'severity'],
    registry=DRIFT_METRICS_REGISTRY
)

concept_drift_detected = Gauge(
    'concept_drift_detected',
    'Concept drift detection status',
    ['severity'],
    registry=DRIFT_METRICS_REGISTRY
)

drift_detection_duration = Histogram(
    'drift_detection_duration_seconds',
    'Time spent on drift detection',
    ['detection_type'],
    registry=DRIFT_METRICS_REGISTRY
)

drift_alerts_triggered = Counter(
    'drift_alerts_triggered_total',
    'Total number of drift alerts triggered',
    ['alert_type', 'severity'],
    registry=DRIFT_METRICS_REGISTRY
)

class DriftDetector:
    """Enhanced class for detecting various types of drift in ML models with monitoring and alerting"""

    def __init__(self, reference_data: pd.DataFrame, config_path: Optional[str] = None):
        """
        Initialize drift detector with reference data and configuration

        Args:
            reference_data: Reference dataset for comparison
            config_path: Path to configuration file (optional)
        """
        self.reference_data = reference_data
        self.reference_stats = self._calculate_reference_stats()
        self.config = self._load_config(config_path)
        self.alert_history = []

        # Initialize thresholds from config
        self.drift_thresholds = self.config.get('drift_thresholds', {
            'feature_drift': 0.05,
            'prediction_drift': 0.05,
            'concept_drift': 0.05,
            'psi_minor': 0.1,
            'psi_moderate': 0.2,
            'psi_severe': 0.5
        })

        # Alert configuration
        self.alert_config = self.config.get('alerting', {
            'enabled': True,
            'channels': ['prometheus', 'log'],
            'severity_levels': ['minor', 'moderate', 'severe'],
            'cooldown_minutes': 60
        })

        # Prometheus gateway configuration
        self.prometheus_gateway = self.config.get('prometheus_gateway', {
            'enabled': False,
            'url': 'http://prometheus-pushgateway:9091',
            'job': 'drift_detection'
        })

    def _load_config(self, config_path: Optional[str]) -> Dict:
        """Load configuration from file or use defaults"""
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    if config_path.endswith('.yml') or config_path.endswith('.yaml'):
                        return yaml.safe_load(f)
                    else:
                        return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load config from {config_path}: {e}")

        # Default configuration
        return {
            'drift_thresholds': {
                'feature_drift': 0.05,
                'prediction_drift': 0.05,
                'concept_drift': 0.05,
                'psi_minor': 0.1,
                'psi_moderate': 0.2,
                'psi_severe': 0.5
            },
            'alerting': {
                'enabled': True,
                'channels': ['prometheus', 'log'],
                'severity_levels': ['minor', 'moderate', 'severe'],
                'cooldown_minutes': 60
            },
            'prometheus_gateway': {
                'enabled': False,
                'url': 'http://prometheus-pushgateway:9091',
                'job': 'drift_detection'
            }
        }
    
    def _calculate_reference_stats(self) -> Dict:
        """Calculate statistics for reference data"""
        stats_dict = {}
        for column in self.reference_data.select_dtypes(include=[np.number]).columns:
            stats_dict[column] = {
                'mean': self.reference_data[column].mean(),
                'std': self.reference_data[column].std(),
                'min': self.reference_data[column].min(),
                'max': self.reference_data[column].max(),
                'quantiles': self.reference_data[column].quantile([0.25, 0.5, 0.75]).to_dict()
            }
        return stats_dict
    
    def detect_feature_drift(self, current_data: pd.DataFrame,
                           threshold: Optional[float] = None) -> Dict:
        """
        Enhanced feature drift detection with monitoring and alerting

        Args:
            current_data: Current dataset to compare
            threshold: P-value threshold for drift detection (uses config if None)

        Returns:
            Dictionary with drift detection results
        """
        start_time = datetime.now()

        if threshold is None:
            threshold = self.drift_thresholds['feature_drift']

        drift_results = {}

        # Get numeric columns
        numeric_columns = current_data.select_dtypes(include=[np.number]).columns
        reference_numeric = self.reference_data[numeric_columns]
        current_numeric = current_data[numeric_columns]

        for column in numeric_columns:
            if column in reference_numeric.columns:
                # Kolmogorov-Smirnov test
                try:
                    ks_statistic, p_value = stats.ks_2samp(
                        reference_numeric[column].dropna(),
                        current_numeric[column].dropna()
                    )

                    severity = self._calculate_drift_severity(p_value)
                    drift_detected = p_value < threshold

                    drift_results[column] = {
                        'ks_statistic': float(ks_statistic),
                        'p_value': float(p_value),
                        'drift_detected': drift_detected,
                        'severity': severity,
                        'threshold_used': threshold
                    }

                    # Update Prometheus metrics
                    if drift_detected:
                        feature_drift_detected.labels(
                            feature_name=column,
                            severity=severity
                        ).set(1)

                        # Trigger alert if enabled
                        if self.alert_config['enabled'] and severity in self.alert_config['severity_levels']:
                            self._trigger_alert('feature_drift', {
                                'feature': column,
                                'severity': severity,
                                'p_value': p_value,
                                'ks_statistic': ks_statistic
                            })
                    else:
                        feature_drift_detected.labels(
                            feature_name=column,
                            severity='none'
                        ).set(0)

                except Exception as e:
                    logger.warning(f"Error calculating KS test for {column}: {e}")
                    drift_results[column] = {
                        'ks_statistic': 0.0,
                        'p_value': 1.0,
                        'drift_detected': False,
                        'severity': 'none',
                        'error': str(e),
                        'threshold_used': threshold
                    }

        # Record timing
        duration = (datetime.now() - start_time).total_seconds()
        drift_detection_duration.labels(detection_type='feature_drift').observe(duration)

        # Push metrics to gateway if configured
        self._push_metrics_to_gateway()

        return drift_results
    
    def calculate_psi(self, current_data: pd.DataFrame,
                     buckets: int = 10) -> Dict:
        """
        Enhanced PSI calculation with monitoring and alerting

        Args:
            current_data: Current dataset to compare
            buckets: Number of buckets for PSI calculation

        Returns:
            Dictionary with PSI results
        """
        start_time = datetime.now()
        psi_results = {}

        # Get numeric columns
        numeric_columns = current_data.select_dtypes(include=[np.number]).columns
        reference_numeric = self.reference_data[numeric_columns]
        current_numeric = current_data[numeric_columns]

        for column in numeric_columns:
            if column in reference_numeric.columns:
                try:
                    # Create bins from reference data
                    bins = np.linspace(
                        min(reference_numeric[column].min(), current_numeric[column].min()),
                        max(reference_numeric[column].max(), current_numeric[column].max()),
                        buckets + 1
                    )

                    # Calculate distributions
                    reference_counts, _ = np.histogram(reference_numeric[column].dropna(), bins=bins)
                    current_counts, _ = np.histogram(current_numeric[column].dropna(), bins=bins)

                    # Normalize to proportions
                    reference_prop = reference_counts / len(reference_numeric[column].dropna())
                    current_prop = current_counts / len(current_numeric[column].dropna())

                    # Avoid division by zero
                    reference_prop = np.where(reference_prop == 0, 0.0001, reference_prop)
                    current_prop = np.where(current_prop == 0, 0.0001, current_prop)

                    # Calculate PSI
                    psi = np.sum((current_prop - reference_prop) * np.log(current_prop / reference_prop))
                    drift_level = self._interpret_psi(psi)

                    psi_results[column] = {
                        'psi': float(psi),
                        'drift_level': drift_level,
                        'reference_distribution': reference_prop.tolist(),
                        'current_distribution': current_prop.tolist(),
                        'buckets_used': buckets
                    }

                    # Update Prometheus metrics
                    psi_drift_score.labels(feature_name=column).set(psi)

                    # Trigger alert for significant PSI drift
                    if drift_level in ['moderate_drift', 'severe_drift'] and self.alert_config['enabled']:
                        severity = 'moderate' if drift_level == 'moderate_drift' else 'severe'
                        if severity in self.alert_config['severity_levels']:
                            self._trigger_alert('psi_drift', {
                                'feature': column,
                                'psi_score': psi,
                                'drift_level': drift_level,
                                'severity': severity
                            })

                except Exception as e:
                    logger.warning(f"Error calculating PSI for {column}: {e}")
                    psi_results[column] = {
                        'psi': 0.0,
                        'drift_level': 'none',
                        'error': str(e),
                        'buckets_used': buckets
                    }

        # Record timing
        duration = (datetime.now() - start_time).total_seconds()
        drift_detection_duration.labels(detection_type='psi_calculation').observe(duration)

        # Push metrics to gateway if configured
        self._push_metrics_to_gateway()

        return psi_results
    
    def detect_prediction_drift(self, reference_predictions: np.array,
                              current_predictions: np.array,
                              model_name: str = 'unknown',
                              threshold: Optional[float] = None) -> Dict:
        """
        Enhanced prediction drift detection with monitoring and alerting

        Args:
            reference_predictions: Reference predictions
            current_predictions: Current predictions
            model_name: Name of the model for tracking
            threshold: P-value threshold for drift detection (uses config if None)

        Returns:
            Dictionary with prediction drift results
        """
        start_time = datetime.now()

        if threshold is None:
            threshold = self.drift_thresholds['prediction_drift']

        try:
            # Kolmogorov-Smirnov test on predictions
            ks_statistic, p_value = stats.ks_2samp(
                reference_predictions,
                current_predictions
            )

            severity = self._calculate_drift_severity(p_value)
            drift_detected = p_value < threshold

            result = {
                'ks_statistic': float(ks_statistic),
                'p_value': float(p_value),
                'drift_detected': drift_detected,
                'severity': severity,
                'model_name': model_name,
                'threshold_used': threshold
            }

            # Update Prometheus metrics
            prediction_drift_detected.labels(
                model_name=model_name,
                severity=severity
            ).set(1 if drift_detected else 0)

            # Trigger alert if drift detected
            if drift_detected and self.alert_config['enabled'] and severity in self.alert_config['severity_levels']:
                self._trigger_alert('prediction_drift', {
                    'model_name': model_name,
                    'severity': severity,
                    'p_value': p_value,
                    'ks_statistic': ks_statistic
                })

            # Record timing
            duration = (datetime.now() - start_time).total_seconds()
            drift_detection_duration.labels(detection_type='prediction_drift').observe(duration)

            # Push metrics to gateway if configured
            self._push_metrics_to_gateway()

            return result

        except Exception as e:
            logger.error(f"Error detecting prediction drift: {e}")
            return {
                'ks_statistic': 0.0,
                'p_value': 1.0,
                'drift_detected': False,
                'severity': 'none',
                'model_name': model_name,
                'error': str(e),
                'threshold_used': threshold
            }
    
    def detect_concept_drift(self, reference_targets: np.array,
                           current_targets: np.array,
                           threshold: Optional[float] = None) -> Dict:
        """
        Enhanced concept drift detection with monitoring and alerting

        Args:
            reference_targets: Reference target values
            current_targets: Current target values
            threshold: P-value threshold for drift detection (uses config if None)

        Returns:
            Dictionary with concept drift results
        """
        start_time = datetime.now()

        if threshold is None:
            threshold = self.drift_thresholds['concept_drift']

        try:
            # Kolmogorov-Smirnov test on target values
            ks_statistic, p_value = stats.ks_2samp(
                reference_targets,
                current_targets
            )

            severity = self._calculate_drift_severity(p_value)
            drift_detected = p_value < threshold

            result = {
                'ks_statistic': float(ks_statistic),
                'p_value': float(p_value),
                'drift_detected': drift_detected,
                'severity': severity,
                'threshold_used': threshold
            }

            # Update Prometheus metrics
            concept_drift_detected.labels(severity=severity).set(1 if drift_detected else 0)

            # Trigger alert if drift detected
            if drift_detected and self.alert_config['enabled'] and severity in self.alert_config['severity_levels']:
                self._trigger_alert('concept_drift', {
                    'severity': severity,
                    'p_value': p_value,
                    'ks_statistic': ks_statistic
                })

            # Record timing
            duration = (datetime.now() - start_time).total_seconds()
            drift_detection_duration.labels(detection_type='concept_drift').observe(duration)

            # Push metrics to gateway if configured
            self._push_metrics_to_gateway()

            return result

        except Exception as e:
            logger.error(f"Error detecting concept drift: {e}")
            return {
                'ks_statistic': 0.0,
                'p_value': 1.0,
                'drift_detected': False,
                'severity': 'none',
                'error': str(e),
                'threshold_used': threshold
            }
    
    def _calculate_drift_severity(self, p_value: float) -> str:
        """Calculate drift severity based on p-value"""
        if p_value < 0.001:
            return 'severe'
        elif p_value < 0.01:
            return 'moderate'
        elif p_value < 0.05:
            return 'minor'
        else:
            return 'none'
    
    def _interpret_psi(self, psi: float) -> str:
        """Interpret PSI value"""
        if psi < 0.1:
            return 'no_drift'
        elif psi < 0.2:
            return 'minor_drift'
        elif psi < 0.5:
            return 'moderate_drift'
        else:
            return 'severe_drift'
    
    def _trigger_alert(self, alert_type: str, alert_data: Dict) -> None:
        """
        Trigger drift alert through configured channels

        Args:
            alert_type: Type of alert (feature_drift, psi_drift, prediction_drift, concept_drift)
            alert_data: Alert data dictionary
        """
        # Check cooldown period
        current_time = datetime.now()
        alert_key = f"{alert_type}_{alert_data.get('feature', alert_data.get('model_name', 'global'))}"

        # Check if we're in cooldown period
        for alert in self.alert_history:
            if (alert['key'] == alert_key and
                current_time - alert['timestamp'] < timedelta(minutes=self.alert_config['cooldown_minutes'])):
                logger.debug(f"Alert {alert_key} in cooldown period, skipping")
                return

        # Create alert
        alert = {
            'key': alert_key,
            'type': alert_type,
            'timestamp': current_time,
            'data': alert_data,
            'severity': alert_data.get('severity', 'unknown')
        }

        # Add to history
        self.alert_history.append(alert)

        # Clean old alerts (keep last 100)
        if len(self.alert_history) > 100:
            self.alert_history = self.alert_history[-100:]

        # Send through configured channels
        if 'log' in self.alert_config['channels']:
            self._log_alert(alert)

        if 'prometheus' in self.alert_config['channels']:
            self._prometheus_alert(alert)

    def _log_alert(self, alert: Dict) -> None:
        """Log alert to logger"""
        severity = alert['severity'].upper()
        alert_type = alert['type']
        data = alert['data']

        if alert_type == 'feature_drift':
            logger.warning(f"[{severity}] Feature drift detected in '{data['feature']}': "
                         f"p-value={data['p_value']:.6f}, KS-statistic={data['ks_statistic']:.6f}")
        elif alert_type == 'psi_drift':
            logger.warning(f"[{severity}] PSI drift detected in '{data['feature']}': "
                         f"PSI={data['psi_score']:.6f}, level={data['drift_level']}")
        elif alert_type == 'prediction_drift':
            logger.warning(f"[{severity}] Prediction drift detected in model '{data['model_name']}': "
                         f"p-value={data['p_value']:.6f}, KS-statistic={data['ks_statistic']:.6f}")
        elif alert_type == 'concept_drift':
            logger.warning(f"[{severity}] Concept drift detected: "
                         f"p-value={data['p_value']:.6f}, KS-statistic={data['ks_statistic']:.6f}")

    def _prometheus_alert(self, alert: Dict) -> None:
        """Update Prometheus alert metrics"""
        drift_alerts_triggered.labels(
            alert_type=alert['type'],
            severity=alert['severity']
        ).inc()

    def _push_metrics_to_gateway(self) -> None:
        """Push metrics to Prometheus gateway if configured"""
        if self.prometheus_gateway['enabled']:
            try:
                from prometheus_client import push_to_gateway
                push_to_gateway(
                    self.prometheus_gateway['url'],
                    job=self.prometheus_gateway['job'],
                    registry=DRIFT_METRICS_REGISTRY
                )
            except Exception as e:
                logger.warning(f"Failed to push metrics to gateway: {e}")

    def generate_drift_report(self, feature_drift: Dict,
                            psi_results: Dict,
                            prediction_drift: Optional[Dict] = None,
                            concept_drift: Optional[Dict] = None) -> Dict:
        """
        Generate enhanced comprehensive drift report with alerting summary

        Args:
            feature_drift: Feature drift results
            psi_results: PSI results
            prediction_drift: Prediction drift results (optional)
            concept_drift: Concept drift results (optional)

        Returns:
            Comprehensive drift report with alerting information
        """
        report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'feature_drift': feature_drift,
            'psi_results': psi_results,
            'prediction_drift': prediction_drift,
            'concept_drift': concept_drift,
            'summary': {},
            'alerting': {
                'alerts_triggered': len([a for a in self.alert_history
                                       if datetime.now() - a['timestamp'] < timedelta(hours=24)]),
                'recent_alerts': self.alert_history[-10:] if self.alert_history else [],
                'config': self.alert_config
            }
        }

        # Count drift detections
        feature_drift_count = sum(1 for result in feature_drift.values()
                                if result.get('drift_detected', False))
        severe_feature_drift = sum(1 for result in feature_drift.values()
                                 if result.get('severity') == 'severe')

        psi_drift_count = sum(1 for result in psi_results.values()
                            if result.get('drift_level') in ['moderate_drift', 'severe_drift'])

        report['summary'] = {
            'total_features': len(feature_drift),
            'features_with_drift': feature_drift_count,
            'severe_feature_drift': severe_feature_drift,
            'features_with_psi_drift': psi_drift_count,
            'prediction_drift_detected': prediction_drift.get('drift_detected', False) if prediction_drift else False,
            'concept_drift_detected': concept_drift.get('drift_detected', False) if concept_drift else False,
            'overall_drift_status': self._calculate_overall_drift_status(
                feature_drift_count, severe_feature_drift, psi_drift_count,
                prediction_drift, concept_drift
            )
        }

        return report

    def _calculate_overall_drift_status(self, feature_drift_count: int, severe_feature_drift: int,
                                      psi_drift_count: int, prediction_drift: Optional[Dict],
                                      concept_drift: Optional[Dict]) -> str:
        """Calculate overall drift status"""
        if severe_feature_drift > 0:
            return 'severe'
        elif (feature_drift_count > len(self.reference_data.columns) * 0.3 or
              psi_drift_count > len(self.reference_data.columns) * 0.3 or
              (prediction_drift and prediction_drift.get('severity') == 'severe') or
              (concept_drift and concept_drift.get('severity') == 'severe')):
            return 'moderate'
        elif (feature_drift_count > 0 or psi_drift_count > 0 or
              (prediction_drift and prediction_drift.get('drift_detected', False)) or
              (concept_drift and concept_drift.get('drift_detected', False))):
            return 'minor'
        else:
            return 'none'

    def run_comprehensive_drift_analysis(self, current_data: pd.DataFrame,
                                        current_predictions: Optional[np.array] = None,
                                        current_targets: Optional[np.array] = None,
                                        model_name: str = 'unknown') -> Dict:
        """
        Run comprehensive drift analysis with all detection methods

        Args:
            current_data: Current dataset to analyze
            current_predictions: Current model predictions (optional)
            current_targets: Current target values (optional)
            model_name: Name of the model for tracking

        Returns:
            Comprehensive drift analysis results
        """
        logger.info(f"Starting comprehensive drift analysis for model: {model_name}")

        # Feature drift detection
        feature_drift = self.detect_feature_drift(current_data)

        # PSI calculation
        psi_results = self.calculate_psi(current_data)

        # Prediction drift (if predictions provided)
        prediction_drift = None
        if current_predictions is not None and len(current_predictions) > 0:
            # Use recent predictions as reference (this would typically come from stored reference predictions)
            reference_predictions = np.random.normal(
                np.mean(current_predictions),
                np.std(current_predictions),
                len(current_predictions)
            )  # Placeholder - in production, use actual reference predictions
            prediction_drift = self.detect_prediction_drift(
                reference_predictions, current_predictions, model_name
            )

        # Concept drift (if targets provided)
        concept_drift = None
        if current_targets is not None and len(current_targets) > 0:
            # Use reference targets from training data
            reference_targets = self.reference_data.select_dtypes(include=[np.number]).iloc[:, 0].values
            if len(reference_targets) > 0:
                concept_drift = self.detect_concept_drift(reference_targets, current_targets)

        # Generate comprehensive report
        report = self.generate_drift_report(
            feature_drift, psi_results, prediction_drift, concept_drift
        )

        logger.info(f"Drift analysis completed. Status: {report['summary']['overall_drift_status']}")

        return report

    def get_drift_metrics_for_prometheus(self) -> Dict[str, Any]:
        """
        Get current drift metrics in Prometheus format

        Returns:
            Dictionary of metrics for Prometheus export
        """
        return {
            'drift_metrics_registry': DRIFT_METRICS_REGISTRY,
            'feature_drift_detected': feature_drift_detected,
            'psi_drift_score': psi_drift_score,
            'prediction_drift_detected': prediction_drift_detected,
            'concept_drift_detected': concept_drift_detected,
            'drift_detection_duration': drift_detection_duration,
            'drift_alerts_triggered': drift_alerts_triggered
        }