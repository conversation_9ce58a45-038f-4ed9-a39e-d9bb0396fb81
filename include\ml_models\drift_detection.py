"""
Model Drift Detection Module
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class DriftDetector:
    """Class for detecting various types of drift in ML models"""
    
    def __init__(self, reference_data: pd.DataFrame):
        """
        Initialize drift detector with reference data
        
        Args:
            reference_data: Reference dataset for comparison
        """
        self.reference_data = reference_data
        self.reference_stats = self._calculate_reference_stats()
    
    def _calculate_reference_stats(self) -> Dict:
        """Calculate statistics for reference data"""
        stats_dict = {}
        for column in self.reference_data.select_dtypes(include=[np.number]).columns:
            stats_dict[column] = {
                'mean': self.reference_data[column].mean(),
                'std': self.reference_data[column].std(),
                'min': self.reference_data[column].min(),
                'max': self.reference_data[column].max(),
                'quantiles': self.reference_data[column].quantile([0.25, 0.5, 0.75]).to_dict()
            }
        return stats_dict
    
    def detect_feature_drift(self, current_data: pd.DataFrame, 
                           threshold: float = 0.05) -> Dict:
        """
        Detect feature drift using Kolmogorov-Smirnov test
        
        Args:
            current_data: Current dataset to compare
            threshold: P-value threshold for drift detection
            
        Returns:
            Dictionary with drift detection results
        """
        drift_results = {}
        
        # Get numeric columns
        numeric_columns = current_data.select_dtypes(include=[np.number]).columns
        reference_numeric = self.reference_data[numeric_columns]
        current_numeric = current_data[numeric_columns]
        
        for column in numeric_columns:
            if column in reference_numeric.columns:
                # Kolmogorov-Smirnov test
                try:
                    ks_statistic, p_value = stats.ks_2samp(
                        reference_numeric[column].dropna(),
                        current_numeric[column].dropna()
                    )
                    
                    drift_results[column] = {
                        'ks_statistic': float(ks_statistic),
                        'p_value': float(p_value),
                        'drift_detected': p_value < threshold,
                        'severity': self._calculate_drift_severity(p_value)
                    }
                except Exception as e:
                    logger.warning(f"Error calculating KS test for {column}: {e}")
                    drift_results[column] = {
                        'ks_statistic': 0.0,
                        'p_value': 1.0,
                        'drift_detected': False,
                        'severity': 'none',
                        'error': str(e)
                    }
        
        return drift_results
    
    def calculate_psi(self, current_data: pd.DataFrame, 
                     buckets: int = 10) -> Dict:
        """
        Calculate Population Stability Index for features
        
        Args:
            current_data: Current dataset to compare
            buckets: Number of buckets for PSI calculation
            
        Returns:
            Dictionary with PSI results
        """
        psi_results = {}
        
        # Get numeric columns
        numeric_columns = current_data.select_dtypes(include=[np.number]).columns
        reference_numeric = self.reference_data[numeric_columns]
        current_numeric = current_data[numeric_columns]
        
        for column in numeric_columns:
            if column in reference_numeric.columns:
                try:
                    # Create bins from reference data
                    bins = np.linspace(
                        min(reference_numeric[column].min(), current_numeric[column].min()),
                        max(reference_numeric[column].max(), current_numeric[column].max()),
                        buckets + 1
                    )
                    
                    # Calculate distributions
                    reference_counts, _ = np.histogram(reference_numeric[column].dropna(), bins=bins)
                    current_counts, _ = np.histogram(current_numeric[column].dropna(), bins=bins)
                    
                    # Normalize to proportions
                    reference_prop = reference_counts / len(reference_numeric[column].dropna())
                    current_prop = current_counts / len(current_numeric[column].dropna())
                    
                    # Avoid division by zero
                    reference_prop = np.where(reference_prop == 0, 0.0001, reference_prop)
                    current_prop = np.where(current_prop == 0, 0.0001, current_prop)
                    
                    # Calculate PSI
                    psi = np.sum((current_prop - reference_prop) * np.log(current_prop / reference_prop))
                    
                    psi_results[column] = {
                        'psi': float(psi),
                        'drift_level': self._interpret_psi(psi),
                        'reference_distribution': reference_prop.tolist(),
                        'current_distribution': current_prop.tolist()
                    }
                except Exception as e:
                    logger.warning(f"Error calculating PSI for {column}: {e}")
                    psi_results[column] = {
                        'psi': 0.0,
                        'drift_level': 'none',
                        'error': str(e)
                    }
        
        return psi_results
    
    def detect_prediction_drift(self, reference_predictions: np.array,
                              current_predictions: np.array,
                              threshold: float = 0.05) -> Dict:
        """
        Detect drift in model predictions
        
        Args:
            reference_predictions: Reference predictions
            current_predictions: Current predictions
            threshold: P-value threshold for drift detection
            
        Returns:
            Dictionary with prediction drift results
        """
        try:
            # Kolmogorov-Smirnov test on predictions
            ks_statistic, p_value = stats.ks_2samp(
                reference_predictions,
                current_predictions
            )
            
            return {
                'ks_statistic': float(ks_statistic),
                'p_value': float(p_value),
                'drift_detected': p_value < threshold,
                'severity': self._calculate_drift_severity(p_value)
            }
        except Exception as e:
            logger.error(f"Error detecting prediction drift: {e}")
            return {
                'ks_statistic': 0.0,
                'p_value': 1.0,
                'drift_detected': False,
                'severity': 'none',
                'error': str(e)
            }
    
    def detect_concept_drift(self, reference_targets: np.array,
                           current_targets: np.array,
                           threshold: float = 0.05) -> Dict:
        """
        Detect concept drift (target distribution changes)
        
        Args:
            reference_targets: Reference target values
            current_targets: Current target values
            threshold: P-value threshold for drift detection
            
        Returns:
            Dictionary with concept drift results
        """
        try:
            # Kolmogorov-Smirnov test on target values
            ks_statistic, p_value = stats.ks_2samp(
                reference_targets,
                current_targets
            )
            
            return {
                'ks_statistic': float(ks_statistic),
                'p_value': float(p_value),
                'drift_detected': p_value < threshold,
                'severity': self._calculate_drift_severity(p_value)
            }
        except Exception as e:
            logger.error(f"Error detecting concept drift: {e}")
            return {
                'ks_statistic': 0.0,
                'p_value': 1.0,
                'drift_detected': False,
                'severity': 'none',
                'error': str(e)
            }
    
    def _calculate_drift_severity(self, p_value: float) -> str:
        """Calculate drift severity based on p-value"""
        if p_value < 0.001:
            return 'severe'
        elif p_value < 0.01:
            return 'moderate'
        elif p_value < 0.05:
            return 'minor'
        else:
            return 'none'
    
    def _interpret_psi(self, psi: float) -> str:
        """Interpret PSI value"""
        if psi < 0.1:
            return 'no_drift'
        elif psi < 0.2:
            return 'minor_drift'
        elif psi < 0.5:
            return 'moderate_drift'
        else:
            return 'severe_drift'
    
    def generate_drift_report(self, feature_drift: Dict, 
                            psi_results: Dict,
                            prediction_drift: Optional[Dict] = None,
                            concept_drift: Optional[Dict] = None) -> Dict:
        """
        Generate comprehensive drift report
        
        Args:
            feature_drift: Feature drift results
            psi_results: PSI results
            prediction_drift: Prediction drift results (optional)
            concept_drift: Concept drift results (optional)
            
        Returns:
            Comprehensive drift report
        """
        report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'feature_drift': feature_drift,
            'psi_results': psi_results,
            'prediction_drift': prediction_drift,
            'concept_drift': concept_drift,
            'summary': {}
        }
        
        # Count drift detections
        feature_drift_count = sum(1 for result in feature_drift.values() 
                                if result.get('drift_detected', False))
        severe_feature_drift = sum(1 for result in feature_drift.values() 
                                 if result.get('severity') == 'severe')
        
        psi_drift_count = sum(1 for result in psi_results.values() 
                            if result.get('drift_level') in ['moderate_drift', 'severe_drift'])
        
        report['summary'] = {
            'total_features': len(feature_drift),
            'features_with_drift': feature_drift_count,
            'severe_feature_drift': severe_feature_drift,
            'features_with_psi_drift': psi_drift_count,
            'prediction_drift_detected': prediction_drift.get('drift_detected', False) if prediction_drift else False,
            'concept_drift_detected': concept_drift.get('drift_detected', False) if concept_drift else False
        }
        
        return report