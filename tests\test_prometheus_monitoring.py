"""
Tests for Prometheus Monitoring Integration
"""

import pytest
import time
import json
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
import numpy as np
from prometheus_client import CollectorReg<PERSON><PERSON>, Gauge, Counter

# Add the include directory to the path
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "include"))

from monitoring.ml_metrics_exporter import MLMetricsExporter
from monitoring.prometheus_integration import PrometheusIntegration


class TestMLMetricsExporter:
    """Test ML Metrics Exporter functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.exporter = MLMetricsExporter(port=8081, update_interval=1)
    
    def teardown_method(self):
        """Cleanup test environment"""
        if self.exporter.running:
            self.exporter.stop_server()
    
    def test_metrics_initialization(self):
        """Test that metrics are properly initialized"""
        assert self.exporter.model_predictions_total is not None
        assert self.exporter.model_prediction_duration is not None
        assert self.exporter.model_prediction_errors is not None
        assert self.exporter.model_accuracy is not None
        assert self.exporter.training_duration is not None
        assert self.exporter.data_quality_score is not None
    
    def test_record_prediction(self):
        """Test recording prediction metrics"""
        # Record a successful prediction
        self.exporter.record_prediction(
            model_name="test_model",
            model_version="1.0",
            duration=0.1,
            status="success"
        )
        
        # Check that metrics were updated
        metrics_output = self.exporter.get_metrics()
        assert b'model_predictions_total' in metrics_output
        assert b'model_prediction_duration_seconds' in metrics_output
    
    def test_record_prediction_error(self):
        """Test recording prediction errors"""
        self.exporter.record_prediction_error(
            model_name="test_model",
            model_version="1.0",
            error_type="validation_error"
        )
        
        metrics_output = self.exporter.get_metrics()
        assert b'model_prediction_errors_total' in metrics_output
    
    @patch('monitoring.ml_metrics_exporter.MlflowClient')
    def test_update_mlflow_metrics(self, mock_mlflow_client):
        """Test updating metrics from MLflow"""
        # Mock MLflow client
        mock_client = Mock()
        mock_mlflow_client.return_value = mock_client
        
        # Mock experiment
        mock_experiment = Mock()
        mock_experiment.name = "sales_forecast_experiment"
        mock_experiment.experiment_id = "1"
        mock_client.search_experiments.return_value = [mock_experiment]
        
        # Mock run
        mock_run = Mock()
        mock_run.info.status = "FINISHED"
        mock_run.info.start_time = 1000000
        mock_run.info.end_time = 1001000
        mock_run.data.tags = {"model_name": "test_model", "model_version": "1.0"}
        mock_run.data.metrics = {"r2": 0.85, "rmse": 0.15}
        mock_client.search_runs.return_value = [mock_run]
        
        # Update metrics
        self.exporter._update_mlflow_metrics()
        
        # Verify metrics were updated
        metrics_output = self.exporter.get_metrics()
        assert b'training_jobs_total' in metrics_output
        assert b'model_accuracy_score' in metrics_output
    
    def test_update_data_quality_metrics(self):
        """Test updating data quality metrics"""
        self.exporter._update_data_quality_metrics()
        
        metrics_output = self.exporter.get_metrics()
        assert b'data_quality_score' in metrics_output
        assert b'data_last_update_timestamp' in metrics_output
    
    def test_update_pipeline_health_metrics(self):
        """Test updating pipeline health metrics"""
        self.exporter._update_pipeline_health_metrics()
        
        metrics_output = self.exporter.get_metrics()
        assert b'pipeline_health_score' in metrics_output
        assert b'service_availability' in metrics_output


class TestPrometheusIntegration:
    """Test Prometheus Integration functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.integration = PrometheusIntegration(
            prometheus_url="http://localhost:9090",
            pushgateway_url="http://localhost:9091"
        )
    
    @patch('monitoring.prometheus_integration.requests.get')
    def test_query_prometheus_success(self, mock_get):
        """Test successful Prometheus query"""
        # Mock successful response
        mock_response = Mock()
        mock_response.json.return_value = {
            'status': 'success',
            'data': {
                'result': [
                    {
                        'metric': {'job': 'test'},
                        'value': [**********, '1.0']
                    }
                ]
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        result = self.integration.query_prometheus('up')
        
        assert result['status'] == 'success'
        assert len(result['data']['result']) == 1
        assert result['data']['result'][0]['value'][1] == '1.0'
    
    @patch('monitoring.prometheus_integration.requests.get')
    def test_query_prometheus_failure(self, mock_get):
        """Test failed Prometheus query"""
        # Mock failed response
        mock_get.side_effect = Exception("Connection error")
        
        result = self.integration.query_prometheus('up')
        
        assert result['status'] == 'error'
        assert 'Connection error' in result['error']
    
    @patch.object(PrometheusIntegration, 'query_prometheus')
    def test_get_ml_pipeline_health(self, mock_query):
        """Test getting ML pipeline health"""
        # Mock service availability queries
        mock_query.side_effect = [
            # MLflow availability
            {
                'status': 'success',
                'data': {'result': [{'value': [**********, '1']}]}
            },
            # Airflow webserver availability
            {
                'status': 'success',
                'data': {'result': [{'value': [**********, '1']}]}
            },
            # Airflow scheduler availability
            {
                'status': 'success',
                'data': {'result': [{'value': [**********, '0']}]}
            },
            # Streamlit UI availability
            {
                'status': 'success',
                'data': {'result': [{'value': [**********, '1']}]}
            },
            # Performance metrics
            {'status': 'success', 'data': {'result': [{'value': [**********, '0.2']}]}},  # latency
            {'status': 'success', 'data': {'result': [{'value': [**********, '0.01']}]}},  # error rate
            {'status': 'success', 'data': {'result': [{'value': [**********, '5']}]}},  # drift rate
            {'status': 'success', 'data': {'result': [{'value': [**********, '0.95']}]}}  # training success
        ]
        
        health = self.integration.get_ml_pipeline_health()
        
        assert 'mlflow_available' in health
        assert 'airflow-webserver_available' in health
        assert 'airflow-scheduler_available' in health
        assert 'streamlit-ui_available' in health
        assert 'overall_health_score' in health
        assert 'health_status' in health
        
        # Check specific values
        assert health['mlflow_available'] is True
        assert health['airflow-scheduler_available'] is False
        assert health['prediction_latency_p95'] == 0.2
        assert health['training_success_rate'] == 0.95
    
    def test_calculate_health_score(self):
        """Test health score calculation"""
        metrics = {
            'mlflow_available': True,
            'airflow-webserver_available': True,
            'airflow-scheduler_available': False,
            'streamlit-ui_available': True,
            'prediction_latency_p95': 0.3,  # Good
            'prediction_error_rate': 0.02,  # Good
            'training_success_rate': 0.9,   # Good
            'drift_detection_rate': 5       # Good
        }
        
        score = self.integration._calculate_health_score(metrics)
        
        # Should be reasonable score since most metrics are good
        # Note: One service is down (scheduler) which affects the score
        assert 0.6 <= score <= 1.0
    
    def test_get_health_status(self):
        """Test health status categorization"""
        assert self.integration._get_health_status(0.95) == 'excellent'
        assert self.integration._get_health_status(0.85) == 'good'
        assert self.integration._get_health_status(0.7) == 'warning'
        assert self.integration._get_health_status(0.5) == 'critical'
    
    @patch.object(PrometheusIntegration, 'query_prometheus')
    def test_get_model_performance_metrics(self, mock_query):
        """Test getting model performance metrics"""
        # Mock performance queries
        mock_query.side_effect = [
            # Prediction count
            {
                'status': 'success',
                'data': {'result': [{'metric': {'model_name': 'test'}, 'value': [**********, '100']}]}
            },
            # Error count
            {
                'status': 'success',
                'data': {'result': [{'metric': {'model_name': 'test'}, 'value': [**********, '2']}]}
            },
            # Average latency
            {
                'status': 'success',
                'data': {'result': [{'metric': {'model_name': 'test'}, 'value': [**********, '0.15']}]}
            },
            # P95 latency
            {
                'status': 'success',
                'data': {'result': [{'metric': {'model_name': 'test'}, 'value': [**********, '0.25']}]}
            },
            # P99 latency
            {
                'status': 'success',
                'data': {'result': [{'metric': {'model_name': 'test'}, 'value': [**********, '0.35']}]}
            }
        ]
        
        metrics = self.integration.get_model_performance_metrics(model_name="test")
        
        assert 'prediction_count' in metrics
        assert 'error_count' in metrics
        assert 'avg_latency' in metrics
        assert 'p95_latency' in metrics
        assert 'p99_latency' in metrics
        
        # Check values
        assert len(metrics['prediction_count']) == 1
        assert metrics['prediction_count'][0]['value'] == 100.0
    
    @patch.object(PrometheusIntegration, 'query_prometheus')
    def test_get_drift_metrics(self, mock_query):
        """Test getting drift metrics"""
        # Mock drift queries
        mock_query.side_effect = [
            # Feature drift
            {
                'status': 'success',
                'data': {'result': [{'metric': {'feature_name': 'price'}, 'value': [**********, '0.3']}]}
            },
            # Concept drift
            {
                'status': 'success',
                'data': {'result': [{'metric': {}, 'value': [**********, '0.1']}]}
            },
            # Prediction drift
            {
                'status': 'success',
                'data': {'result': [{'metric': {}, 'value': [**********, '0.2']}]}
            },
            # PSI scores
            {
                'status': 'success',
                'data': {'result': [{'metric': {'feature_name': 'quantity'}, 'value': [**********, '0.15']}]}
            },
            # Drift alerts
            {
                'status': 'success',
                'data': {'result': [{'metric': {}, 'value': [**********, '5']}]}
            }
        ]
        
        metrics = self.integration.get_drift_metrics()
        
        assert 'feature_drift' in metrics
        assert 'concept_drift' in metrics
        assert 'prediction_drift' in metrics
        assert 'psi_scores' in metrics
        assert 'drift_alerts' in metrics
    
    @patch('monitoring.prometheus_integration.push_to_gateway')
    def test_push_custom_metrics(self, mock_push):
        """Test pushing custom metrics"""
        registry = CollectorRegistry()
        test_gauge = Gauge('test_metric', 'Test metric', registry=registry)
        test_gauge.set(42)
        
        result = self.integration.push_custom_metrics(
            job_name="test_job",
            metrics_registry=registry,
            labels={"instance": "test"}
        )
        
        assert result is True
        mock_push.assert_called_once()
    
    @patch('monitoring.prometheus_integration.requests.get')
    def test_get_active_alerts(self, mock_get):
        """Test getting active alerts"""
        # Mock alerts response
        mock_response = Mock()
        mock_response.json.return_value = {
            'status': 'success',
            'data': {
                'alerts': [
                    {
                        'labels': {
                            'alertname': 'ModelDriftAlert',
                            'severity': 'warning',
                            'model_name': 'sales_forecast'
                        },
                        'state': 'firing'
                    },
                    {
                        'labels': {
                            'alertname': 'HighCPUUsage',
                            'severity': 'critical'
                        },
                        'state': 'firing'
                    }
                ]
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        alerts = self.integration.get_active_alerts()
        
        assert len(alerts) == 1  # Only ML-related alert should be returned
        assert alerts[0]['labels']['alertname'] == 'ModelDriftAlert'
    
    @patch.object(PrometheusIntegration, 'get_ml_pipeline_health')
    @patch.object(PrometheusIntegration, 'get_model_performance_metrics')
    @patch.object(PrometheusIntegration, 'get_drift_metrics')
    @patch.object(PrometheusIntegration, 'get_training_pipeline_metrics')
    @patch.object(PrometheusIntegration, 'get_active_alerts')
    def test_create_monitoring_summary(self, mock_alerts, mock_training, 
                                     mock_drift, mock_performance, mock_health):
        """Test creating comprehensive monitoring summary"""
        # Mock all the component methods
        mock_health.return_value = {'overall_health_score': 0.85}
        mock_performance.return_value = {'prediction_count': []}
        mock_drift.return_value = {'feature_drift': []}
        mock_training.return_value = {'training_jobs_success': []}
        mock_alerts.return_value = []
        
        summary = self.integration.create_monitoring_summary()
        
        assert 'timestamp' in summary
        assert 'pipeline_health' in summary
        assert 'model_performance' in summary
        assert 'drift_status' in summary
        assert 'training_status' in summary
        assert 'active_alerts' in summary
    
    def test_export_metrics_for_grafana(self):
        """Test exporting metrics for Grafana"""
        with patch.object(self.integration, 'create_monitoring_summary') as mock_summary:
            mock_summary.return_value = {
                'pipeline_health': {
                    'overall_health_score': 0.9,
                    'health_status': 'excellent',
                    'mlflow_available': True,
                    'airflow-webserver_available': True
                },
                'model_performance': {},
                'drift_status': {},
                'training_status': {},
                'active_alerts': []
            }
            
            grafana_data = self.integration.export_metrics_for_grafana()
            
            assert 'dashboard_data' in grafana_data
            assert 'time_series_data' in grafana_data
            assert 'alerts' in grafana_data
            
            dashboard_data = grafana_data['dashboard_data']
            assert dashboard_data['health_score'] == 0.9
            assert dashboard_data['health_status'] == 'excellent'
            assert dashboard_data['active_alerts_count'] == 0


if __name__ == "__main__":
    pytest.main([__file__])
