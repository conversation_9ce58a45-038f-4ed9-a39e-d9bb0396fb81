# Astro Runtime includes the following pre-installed providers packages: https://www.astronomer.io/docs/astro/runtime-image-architecture#provider-packages

# ML and Data Processing
pandas
numpy
scipy
scikit-learn
xgboost
lightgbm
prophet
statsmodels

# MLFlow for experiment tracking
mlflow
boto3==1.40.49

# Data Validation
pandera

# Feature Engineering
category-encoders

# Model serving and API
# FastAPI dependencies removed per user request

# Data Visualization
matplotlib
seaborn
plotly

# Database connections
sqlalchemy
psycopg2-binary
pymongo

# Cloud Storage
s3fs==2025.9.0

# Testing
pytest

# Additional packages
python-dotenv
PyYAML
joblib
optuna
shap
holidays

# Monitoring
prometheus-client

# UI
streamlit



astro-run-dag # This package is needed for the astro run command. It will be removed before a deploy