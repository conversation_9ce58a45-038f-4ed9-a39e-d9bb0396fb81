"""
Prometheus Integration for ML Pipeline Monitoring
"""

import os
import json
import logging
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from prometheus_client import CollectorRegistry, push_to_gateway, delete_from_gateway

logger = logging.getLogger(__name__)


class PrometheusIntegration:
    """
    Integration layer for Prometheus monitoring in ML pipeline
    """
    
    def __init__(self, prometheus_url: str = None, pushgateway_url: str = None):
        self.prometheus_url = prometheus_url or os.getenv('PROMETHEUS_URL', 'http://prometheus:9090')
        self.pushgateway_url = pushgateway_url or os.getenv('PUSHGATEWAY_URL', 'http://pushgateway:9091')
        
    def query_prometheus(self, query: str, time_range: Optional[str] = None) -> Dict:
        """
        Query Prometheus for metrics data
        """
        try:
            if time_range:
                url = f"{self.prometheus_url}/api/v1/query_range"
                params = {
                    'query': query,
                    'start': time_range,
                    'end': datetime.now().isoformat(),
                    'step': '1m'
                }
            else:
                url = f"{self.prometheus_url}/api/v1/query"
                params = {'query': query}
            
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Failed to query Prometheus: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_ml_pipeline_health(self) -> Dict:
        """
        Get overall ML pipeline health status
        """
        health_metrics = {}
        
        try:
            # Check service availability
            services = ['mlflow', 'airflow-webserver', 'airflow-scheduler', 'streamlit-ui']
            for service in services:
                query = f'up{{job="{service}"}}'
                result = self.query_prometheus(query)
                
                if result.get('status') == 'success' and result.get('data', {}).get('result'):
                    value = float(result['data']['result'][0]['value'][1])
                    health_metrics[f'{service}_available'] = value == 1.0
                else:
                    health_metrics[f'{service}_available'] = False
            
            # Check model performance
            performance_queries = {
                'prediction_latency_p95': 'ml:prediction_latency_p95',
                'prediction_error_rate': 'ml:prediction_error_rate',
                'drift_detection_rate': 'ml:drift_detection_rate',
                'training_success_rate': 'ml:training_success_rate'
            }
            
            for metric_name, query in performance_queries.items():
                result = self.query_prometheus(query)
                if result.get('status') == 'success' and result.get('data', {}).get('result'):
                    value = float(result['data']['result'][0]['value'][1])
                    health_metrics[metric_name] = value
                else:
                    health_metrics[metric_name] = None
            
            # Calculate overall health score
            health_score = self._calculate_health_score(health_metrics)
            health_metrics['overall_health_score'] = health_score
            health_metrics['health_status'] = self._get_health_status(health_score)
            
        except Exception as e:
            logger.error(f"Failed to get ML pipeline health: {e}")
            health_metrics['error'] = str(e)
        
        return health_metrics
    
    def _calculate_health_score(self, metrics: Dict) -> float:
        """Calculate overall health score from individual metrics"""
        score = 0.0
        total_weight = 0.0
        
        # Service availability (40% weight)
        service_weights = {
            'mlflow_available': 0.15,
            'airflow-webserver_available': 0.10,
            'airflow-scheduler_available': 0.10,
            'streamlit-ui_available': 0.05
        }
        
        for service, weight in service_weights.items():
            if metrics.get(service) is not None:
                score += weight if metrics[service] else 0
                total_weight += weight
        
        # Performance metrics (60% weight)
        performance_weights = {
            'prediction_latency_p95': (0.15, lambda x: max(0, 1 - x/0.5)),  # Good if < 500ms
            'prediction_error_rate': (0.15, lambda x: max(0, 1 - x/0.05)),  # Good if < 5%
            'training_success_rate': (0.15, lambda x: x if x is not None else 0),  # Direct score
            'drift_detection_rate': (0.15, lambda x: max(0, 1 - x/10))  # Good if < 10 alerts/hour
        }
        
        for metric, (weight, score_func) in performance_weights.items():
            value = metrics.get(metric)
            if value is not None:
                score += weight * score_func(value)
                total_weight += weight
        
        return score / total_weight if total_weight > 0 else 0.0
    
    def _get_health_status(self, score: float) -> str:
        """Get health status from score"""
        if score >= 0.9:
            return 'excellent'
        elif score >= 0.8:
            return 'good'
        elif score >= 0.6:
            return 'warning'
        else:
            return 'critical'
    
    def get_model_performance_metrics(self, model_name: str = None, 
                                    time_range: str = '1h') -> Dict:
        """
        Get model performance metrics
        """
        metrics = {}
        
        try:
            # Base query filter
            model_filter = f'{{model_name="{model_name}"}}' if model_name else ''
            
            # Performance queries
            queries = {
                'prediction_count': f'increase(model_predictions_total{model_filter}[{time_range}])',
                'error_count': f'increase(model_prediction_errors_total{model_filter}[{time_range}])',
                'avg_latency': f'avg(rate(model_prediction_duration_seconds_sum{model_filter}[5m])) / avg(rate(model_prediction_duration_seconds_count{model_filter}[5m]))',
                'p95_latency': f'histogram_quantile(0.95, rate(model_prediction_duration_seconds_bucket{model_filter}[5m]))',
                'p99_latency': f'histogram_quantile(0.99, rate(model_prediction_duration_seconds_bucket{model_filter}[5m]))'
            }
            
            for metric_name, query in queries.items():
                result = self.query_prometheus(query)
                if result.get('status') == 'success' and result.get('data', {}).get('result'):
                    metrics[metric_name] = [
                        {
                            'labels': item.get('metric', {}),
                            'value': float(item['value'][1])
                        }
                        for item in result['data']['result']
                    ]
                else:
                    metrics[metric_name] = []
            
        except Exception as e:
            logger.error(f"Failed to get model performance metrics: {e}")
            metrics['error'] = str(e)
        
        return metrics
    
    def get_drift_metrics(self, time_range: str = '24h') -> Dict:
        """
        Get drift detection metrics
        """
        metrics = {}
        
        try:
            queries = {
                'feature_drift': f'feature_drift_detected',
                'concept_drift': f'concept_drift_detected',
                'prediction_drift': f'prediction_drift_detected',
                'psi_scores': f'psi_drift_score',
                'drift_alerts': f'increase(drift_alerts_triggered_total[{time_range}])'
            }
            
            for metric_name, query in queries.items():
                result = self.query_prometheus(query)
                if result.get('status') == 'success' and result.get('data', {}).get('result'):
                    metrics[metric_name] = [
                        {
                            'labels': item.get('metric', {}),
                            'value': float(item['value'][1]),
                            'timestamp': item['value'][0]
                        }
                        for item in result['data']['result']
                    ]
                else:
                    metrics[metric_name] = []
            
        except Exception as e:
            logger.error(f"Failed to get drift metrics: {e}")
            metrics['error'] = str(e)
        
        return metrics
    
    def get_training_pipeline_metrics(self, time_range: str = '7d') -> Dict:
        """
        Get training pipeline metrics
        """
        metrics = {}
        
        try:
            queries = {
                'training_jobs_success': f'increase(training_jobs_total{{status="success"}}[{time_range}])',
                'training_jobs_failed': f'increase(training_jobs_total{{status="failed"}}[{time_range}])',
                'training_duration': f'avg(training_duration_seconds)',
                'model_versions': f'model_versions_total',
                'dag_runs_success': f'increase(airflow_dag_run_success_total{{dag_id=~"sales_forecast_.*"}}[{time_range}])',
                'dag_runs_failed': f'increase(airflow_dag_run_failed_total{{dag_id=~"sales_forecast_.*"}}[{time_range}])'
            }
            
            for metric_name, query in queries.items():
                result = self.query_prometheus(query)
                if result.get('status') == 'success' and result.get('data', {}).get('result'):
                    metrics[metric_name] = [
                        {
                            'labels': item.get('metric', {}),
                            'value': float(item['value'][1])
                        }
                        for item in result['data']['result']
                    ]
                else:
                    metrics[metric_name] = []
            
        except Exception as e:
            logger.error(f"Failed to get training pipeline metrics: {e}")
            metrics['error'] = str(e)
        
        return metrics
    
    def push_custom_metrics(self, job_name: str, metrics_registry: CollectorRegistry,
                          labels: Optional[Dict] = None) -> bool:
        """
        Push custom metrics to Pushgateway
        """
        try:
            grouping_key = labels or {}
            push_to_gateway(
                self.pushgateway_url,
                job=job_name,
                registry=metrics_registry,
                grouping_key=grouping_key
            )
            logger.info(f"Successfully pushed metrics for job: {job_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to push metrics to Pushgateway: {e}")
            return False
    
    def delete_custom_metrics(self, job_name: str, labels: Optional[Dict] = None) -> bool:
        """
        Delete custom metrics from Pushgateway
        """
        try:
            grouping_key = labels or {}
            delete_from_gateway(
                self.pushgateway_url,
                job=job_name,
                grouping_key=grouping_key
            )
            logger.info(f"Successfully deleted metrics for job: {job_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete metrics from Pushgateway: {e}")
            return False
    
    def get_active_alerts(self) -> List[Dict]:
        """
        Get active alerts from Prometheus
        """
        try:
            url = f"{self.prometheus_url}/api/v1/alerts"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get('status') == 'success':
                alerts = data.get('data', {}).get('alerts', [])
                
                # Filter for ML pipeline related alerts
                ml_alerts = [
                    alert for alert in alerts
                    if any(label in str(alert.get('labels', {})).lower() 
                          for label in ['ml', 'model', 'drift', 'training', 'prediction'])
                ]
                
                return ml_alerts
            else:
                logger.warning(f"Failed to get alerts: {data}")
                return []
                
        except Exception as e:
            logger.error(f"Failed to get active alerts: {e}")
            return []
    
    def create_monitoring_summary(self) -> Dict:
        """
        Create comprehensive monitoring summary
        """
        summary = {
            'timestamp': datetime.now().isoformat(),
            'pipeline_health': self.get_ml_pipeline_health(),
            'model_performance': self.get_model_performance_metrics(time_range='1h'),
            'drift_status': self.get_drift_metrics(time_range='24h'),
            'training_status': self.get_training_pipeline_metrics(time_range='7d'),
            'active_alerts': self.get_active_alerts()
        }
        
        return summary
    
    def export_metrics_for_grafana(self, output_file: str = None) -> Dict:
        """
        Export metrics in format suitable for Grafana dashboard
        """
        try:
            summary = self.create_monitoring_summary()
            
            # Format for Grafana
            grafana_data = {
                'dashboard_data': {
                    'health_score': summary['pipeline_health'].get('overall_health_score', 0),
                    'health_status': summary['pipeline_health'].get('health_status', 'unknown'),
                    'active_alerts_count': len(summary['active_alerts']),
                    'services_status': {
                        k: v for k, v in summary['pipeline_health'].items()
                        if k.endswith('_available')
                    }
                },
                'time_series_data': {
                    'model_performance': summary['model_performance'],
                    'drift_metrics': summary['drift_status'],
                    'training_metrics': summary['training_status']
                },
                'alerts': summary['active_alerts']
            }
            
            if output_file:
                with open(output_file, 'w') as f:
                    json.dump(grafana_data, f, indent=2)
                logger.info(f"Exported Grafana data to {output_file}")
            
            return grafana_data
            
        except Exception as e:
            logger.error(f"Failed to export metrics for Grafana: {e}")
            return {'error': str(e)}


# Global Prometheus integration instance
prometheus_integration = None


def get_prometheus_integration() -> PrometheusIntegration:
    """Get global Prometheus integration instance"""
    global prometheus_integration
    
    if prometheus_integration is None:
        prometheus_integration = PrometheusIntegration()
    
    return prometheus_integration


# Export main classes and functions
__all__ = [
    'PrometheusIntegration',
    'get_prometheus_integration'
]
