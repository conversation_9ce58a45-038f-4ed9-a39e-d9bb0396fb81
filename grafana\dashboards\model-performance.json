{"dashboard": {"id": null, "title": "Model Performance", "timezone": "browser", "schemaVersion": 16, "version": 0, "refresh": "5s", "panels": [{"id": 1, "type": "gauge", "title": "Current Model Accuracy", "gridPos": {"x": 0, "y": 0, "w": 6, "h": 6}, "targets": [{"expr": "model_accuracy_score", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "options": {"fieldOptions": {"calcs": ["lastNotNull"]}}}, {"id": 2, "type": "graph", "title": "Training Duration", "gridPos": {"x": 6, "y": 0, "w": 12, "h": 6}, "targets": [{"expr": "rate(training_duration_seconds_sum[5m]) / rate(training_duration_seconds_count[5m])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 3, "type": "stat", "title": "Models Trained", "gridPos": {"x": 0, "y": 6, "w": 6, "h": 4}, "targets": [{"expr": "increase(models_trained_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}, {"id": 4, "type": "graph", "title": "Training Errors", "gridPos": {"x": 6, "y": 6, "w": 12, "h": 4}, "targets": [{"expr": "increase(training_errors_total[1h])", "format": "time_series", "intervalFactor": 1, "refId": "A"}]}]}}