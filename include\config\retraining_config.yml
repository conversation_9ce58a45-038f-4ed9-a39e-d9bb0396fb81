# Automated Model Retraining Configuration

# Retraining triggers
triggers:
  # Time-based triggers
  time_based:
    enabled: true
    max_days_since_training: 7  # Retrain weekly
    force_retrain_days: 30      # Force retrain monthly regardless of performance
  
  # Performance-based triggers
  performance_based:
    enabled: true
    performance_degradation_threshold: 0.05  # 5% degradation
    consecutive_poor_performance_days: 3
    
  # Drift-based triggers (integrated with drift detection system)
  drift_based:
    enabled: true
    feature_drift_threshold: 0.3
    concept_drift_threshold: 0.2
    prediction_drift_threshold: 0.25
    
  # Data availability triggers
  data_based:
    enabled: true
    min_new_data_points: 1000
    data_quality_threshold: 0.7

# Data preparation
data_preparation:
  # Training data window
  training_window_days: 180  # 6 months of data
  validation_split: 0.2
  test_split: 0.1
  
  # Data quality checks
  quality_checks:
    min_data_points: 1000
    max_missing_values_ratio: 0.1
    max_outliers_ratio: 0.05
    required_columns:
      - date
      - store_id
      - sales
      - quantity_sold
      - profit
    
  # Feature engineering
  feature_engineering:
    create_lag_features: true
    lag_periods: [1, 7, 14, 30]
    create_rolling_features: true
    rolling_windows: [7, 14, 30]
    create_seasonal_features: true

# Model training configuration
training:
  # Models to train
  models:
    - linear_regression
    - random_forest
    - xgboost
    - lightgbm
    
  # Hyperparameter optimization
  hyperparameter_optimization:
    enabled: true
    optimization_framework: optuna
    n_trials: 100
    timeout_minutes: 60
    
  # Cross-validation
  cross_validation:
    enabled: true
    n_folds: 5
    strategy: time_series_split
    
  # Early stopping
  early_stopping:
    enabled: true
    patience: 10
    min_delta: 0.001

# Model validation
validation:
  # Performance thresholds
  performance_thresholds:
    min_r2_score: 0.7
    max_rmse_increase_ratio: 1.2  # Compared to baseline
    min_data_points: 1000
    max_prediction_time_ms: 100
    
  # Stability testing
  stability_testing:
    enabled: true
    bootstrap_samples: 100
    stability_threshold: 0.2  # Coefficient of variation
    
  # Business rule validation
  business_rules:
    max_negative_predictions_ratio: 0.01
    prediction_range_multiplier: 3.0
    seasonal_consistency_threshold: 0.85
    
  # A/B testing configuration
  ab_testing:
    enabled: false  # Enable for gradual rollout
    traffic_split: 0.1  # 10% traffic to new model initially
    success_metrics:
      - prediction_accuracy
      - response_time
      - user_satisfaction

# Model deployment
deployment:
  # Deployment strategy
  strategy: blue_green  # Options: blue_green, canary, rolling
  
  # Staging deployment
  staging:
    enabled: true
    validation_period_hours: 24
    auto_promote_to_production: false
    
  # Production deployment
  production:
    auto_deploy_threshold: 0.8  # Confidence score threshold
    require_manual_approval: true
    rollback_on_performance_drop: true
    
  # Canary deployment (if strategy is canary)
  canary:
    initial_traffic_percentage: 5
    increment_percentage: 10
    increment_interval_hours: 2
    success_threshold: 0.95

# Model versioning
versioning:
  # Version naming
  version_naming_strategy: semantic  # Options: semantic, timestamp, sequential
  
  # Model registry
  model_registry:
    enabled: true
    registry_name: sales_forecast_models
    
  # Model archival
  archival:
    keep_production_versions: 5
    keep_staging_versions: 10
    archive_after_days: 90
    
  # Model comparison
  comparison:
    compare_with_production: true
    compare_with_previous_versions: 3
    comparison_metrics:
      - rmse
      - mae
      - r2
      - mape

# Monitoring and alerting
monitoring:
  # Performance monitoring
  performance_monitoring:
    enabled: true
    check_interval_minutes: 15
    alert_on_degradation: true
    degradation_threshold: 0.05
    
  # Drift monitoring integration
  drift_monitoring:
    enabled: true
    check_interval_hours: 6
    alert_on_drift: true
    
  # Resource monitoring
  resource_monitoring:
    enabled: true
    max_memory_usage_mb: 2048
    max_cpu_usage_percent: 80
    max_prediction_time_ms: 100

# Notifications
notifications:
  # Notification channels
  channels:
    email:
      enabled: true
      recipients:
        - <EMAIL>
        - <EMAIL>
    slack:
      enabled: false
      webhook_url: ""
      channel: "#ml-alerts"
    teams:
      enabled: false
      webhook_url: ""
      
  # Notification triggers
  triggers:
    retraining_started: true
    retraining_completed: true
    retraining_failed: true
    model_deployed: true
    model_rollback: true
    performance_degradation: true
    validation_failed: true

# Logging and auditing
logging:
  # Log levels
  level: INFO
  
  # Log destinations
  destinations:
    file:
      enabled: true
      path: /var/log/ml_retraining.log
      max_size_mb: 100
      backup_count: 5
    mlflow:
      enabled: true
      log_artifacts: true
      log_metrics: true
      log_params: true
    prometheus:
      enabled: true
      push_gateway_url: http://prometheus-pushgateway:9091
      
  # Audit trail
  audit_trail:
    enabled: true
    track_data_lineage: true
    track_model_lineage: true
    track_deployment_history: true

# Resource management
resources:
  # Compute resources
  compute:
    max_parallel_training_jobs: 2
    training_timeout_hours: 4
    memory_limit_gb: 8
    cpu_limit_cores: 4
    
  # Storage resources
  storage:
    temp_data_retention_days: 7
    model_artifact_retention_days: 365
    log_retention_days: 90
    
  # MLflow resources
  mlflow:
    experiment_name: sales_forecast_retraining
    tracking_uri: http://mlflow:5000
    artifact_store: s3://mlflow-artifacts
    
# Error handling and recovery
error_handling:
  # Retry configuration
  retry:
    max_retries: 3
    retry_delay_minutes: 5
    exponential_backoff: true
    
  # Fallback strategies
  fallback:
    use_previous_model_on_failure: true
    alert_on_fallback: true
    
  # Recovery procedures
  recovery:
    auto_recovery_enabled: true
    manual_intervention_threshold: 3  # failures
    escalation_contacts:
      - <EMAIL>
      - <EMAIL>

# Feature flags
feature_flags:
  enable_advanced_validation: true
  enable_model_explainability: true
  enable_performance_profiling: true
  enable_automated_rollback: true
  enable_multi_model_ensemble: false
  enable_online_learning: false
