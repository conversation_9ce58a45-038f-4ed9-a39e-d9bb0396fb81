# Monitoring Configuration for Sales Prediction ML Pipeline

# Prometheus Configuration
prometheus:
  url: "http://prometheus:9090"
  scrape_interval: 15s
  evaluation_interval: 15s
  retention_time: "200h"
  
  # Query timeouts
  query_timeout: 30s
  scrape_timeout: 10s
  
  # External labels
  external_labels:
    cluster: "sales-prediction-ml"
    environment: "production"

# Pushgateway Configuration
pushgateway:
  url: "http://pushgateway:9091"
  push_timeout: 30s
  job_prefix: "ml_pipeline"

# ML Metrics Exporter Configuration
ml_metrics_exporter:
  port: 8080
  update_interval: 60  # seconds
  
  # Metrics collection settings
  collection:
    mlflow_metrics: true
    system_metrics: true
    data_quality_metrics: true
    pipeline_health_metrics: true
    business_metrics: true
  
  # MLflow integration
  mlflow:
    tracking_uri: "http://mlflow:5001"
    max_runs_per_experiment: 10
    metrics_retention_days: 30
  
  # System monitoring
  system:
    enable_psutil: true
    memory_threshold_mb: 8192
    cpu_threshold_percent: 80
    disk_threshold_percent: 90

# Alerting Configuration
alerting:
  enabled: true
  
  # Alert thresholds
  thresholds:
    # Model Performance
    prediction_latency_p95_ms: 500
    prediction_error_rate_percent: 5.0
    model_accuracy_min: 0.8
    
    # Drift Detection
    feature_drift_warning: 0.3
    feature_drift_critical: 0.5
    concept_drift_warning: 0.2
    concept_drift_critical: 0.4
    prediction_drift_warning: 0.25
    prediction_drift_critical: 0.45
    psi_drift_warning: 0.2
    psi_drift_critical: 0.3
    
    # Training Pipeline
    training_duration_max_hours: 6
    training_failure_rate_percent: 10.0
    
    # Data Quality
    data_quality_min_score: 0.7
    missing_values_max_percent: 20.0
    data_freshness_max_hours: 24
    
    # System Resources
    memory_usage_percent: 90
    cpu_usage_percent: 80
    disk_usage_percent: 90
    
    # Service Availability
    service_downtime_max_minutes: 2
  
  # Alert severity levels
  severity_levels:
    - info
    - warning
    - critical
  
  # Cooldown periods (to prevent alert spam)
  cooldown:
    warning: 300  # 5 minutes
    critical: 60   # 1 minute
  
  # Alert routing
  routing:
    default_receiver: "ml-team"
    routes:
      - match:
          severity: critical
        receiver: "on-call"
      - match:
          component: drift_detection
        receiver: "data-science-team"
      - match:
          component: training_pipeline
        receiver: "ml-ops-team"

# Health Check Configuration
health_checks:
  enabled: true
  interval: 30  # seconds
  
  # Service endpoints to check
  services:
    mlflow:
      url: "http://mlflow:5001/health"
      timeout: 10
      expected_status: 200
    
    airflow_webserver:
      url: "http://airflow-webserver:8080/health"
      timeout: 10
      expected_status: 200
    
    airflow_scheduler:
      url: "http://airflow-scheduler:8080/health"
      timeout: 10
      expected_status: 200
    
    streamlit_ui:
      url: "http://streamlit-ui:8501/_stcore/health"
      timeout: 10
      expected_status: 200
    
    prometheus:
      url: "http://prometheus:9090/-/healthy"
      timeout: 10
      expected_status: 200
    
    pushgateway:
      url: "http://pushgateway:9091/-/healthy"
      timeout: 10
      expected_status: 200

# Dashboard Configuration
dashboards:
  grafana:
    url: "http://grafana:3000"
    admin_user: "admin"
    admin_password: "admin"
    
    # Dashboard provisioning
    provisioning:
      enabled: true
      path: "/etc/grafana/provisioning"
    
    # Data sources
    datasources:
      - name: "Prometheus"
        type: "prometheus"
        url: "http://prometheus:9090"
        access: "proxy"
        is_default: true
    
    # Dashboard refresh intervals
    refresh_intervals:
      - "5s"
      - "10s"
      - "30s"
      - "1m"
      - "5m"
      - "15m"
      - "30m"
      - "1h"
      - "2h"
      - "1d"

# Metrics Retention Configuration
retention:
  # Prometheus metrics retention
  prometheus_retention: "200h"
  
  # Custom metrics retention
  custom_metrics:
    high_frequency: "7d"    # Metrics collected every second/minute
    medium_frequency: "30d" # Metrics collected every 5-15 minutes
    low_frequency: "90d"    # Metrics collected hourly/daily
  
  # Log retention
  logs:
    application_logs: "30d"
    system_logs: "7d"
    audit_logs: "90d"

# Performance Configuration
performance:
  # Query optimization
  max_concurrent_queries: 20
  query_timeout: "2m"
  
  # Memory limits
  prometheus_memory_limit: "2GB"
  grafana_memory_limit: "512MB"
  
  # Storage optimization
  prometheus_storage_retention: "200h"
  prometheus_storage_retention_size: "10GB"

# Security Configuration
security:
  # Authentication
  authentication:
    enabled: false  # Set to true in production
    method: "basic"  # basic, oauth, ldap
  
  # Authorization
  authorization:
    enabled: false  # Set to true in production
    rbac: false
  
  # TLS/SSL
  tls:
    enabled: false  # Set to true in production
    cert_file: "/etc/ssl/certs/monitoring.crt"
    key_file: "/etc/ssl/private/monitoring.key"
  
  # Network security
  network:
    allowed_ips: []  # Empty means all IPs allowed
    firewall_enabled: false

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  
  # Log formats
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # Log destinations
  handlers:
    console:
      enabled: true
      level: "INFO"
    
    file:
      enabled: true
      level: "DEBUG"
      filename: "/var/log/ml_monitoring.log"
      max_size_mb: 100
      backup_count: 5
    
    syslog:
      enabled: false
      level: "WARNING"
      facility: "local0"

# Integration Configuration
integrations:
  # Slack notifications
  slack:
    enabled: false
    webhook_url: ""
    channel: "#ml-alerts"
    username: "ML-Monitor"
  
  # Email notifications
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    from_address: "<EMAIL>"
    to_addresses: []
  
  # PagerDuty integration
  pagerduty:
    enabled: false
    integration_key: ""
    severity_mapping:
      critical: "critical"
      warning: "warning"
      info: "info"
  
  # Webhook notifications
  webhooks:
    enabled: false
    endpoints: []

# Feature Flags
feature_flags:
  enable_advanced_metrics: true
  enable_ml_model_monitoring: true
  enable_data_drift_detection: true
  enable_concept_drift_detection: true
  enable_prediction_monitoring: true
  enable_training_pipeline_monitoring: true
  enable_system_monitoring: true
  enable_business_metrics: true
  enable_custom_dashboards: true
  enable_automated_alerting: true
  enable_anomaly_detection: false  # Future feature
  enable_predictive_alerting: false  # Future feature
