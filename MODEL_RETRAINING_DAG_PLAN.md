# Model Retraining DAG Implementation Plan

## Overview
This document outlines the implementation plan for creating an automated model retraining pipeline using Apache Airflow.

## Implementation Steps

### Step 1: Create Retraining DAG Structure
- Create `dags/sales_forecast_retraining.py`
- Define DAG parameters and schedule
- Set up task dependencies

### Step 2: Implement Data Quality Checks
- Add data validation tasks
- Implement data drift detection
- Add data quality scoring

### Step 3: Implement Model Training Logic
- Add model training tasks
- Implement hyperparameter optimization
- Add model evaluation

### Step 4: Implement Model Registration
- Add model versioning
- Implement model comparison
- Add model promotion logic

## Detailed Task Breakdown

### Task 1: Create Retraining DAG Structure
**Estimated Time**: 1 hour

Subtasks:
1. Create `dags/sales_forecast_retraining.py`
2. Define DAG parameters (schedule, start date, etc.)
3. Set up task flow using TaskFlow API
4. Add basic task structure
5. Test DAG registration

### Task 2: Implement Data Quality Checks
**Estimated Time**: 2 hours

Subtasks:
1. Create data validation tasks
2. Implement data drift detection
3. Add data quality scoring
4. Create data quality reports
5. Add quality-based triggering logic

### Task 3: Implement Model Training Logic
**Estimated Time**: 3 hours

Subtasks:
1. Add model training tasks
2. Implement hyperparameter optimization with Optuna
3. Add cross-validation
4. Implement model evaluation
5. Add model serialization

### Task 4: Implement Model Registration
**Estimated Time**: 2 hours

Subtasks:
1. Add model versioning
2. Implement model comparison logic
3. Add model promotion to production
4. Implement rollback mechanism
5. Add registration confirmation

## Implementation Details

### DAG Structure

#### File: dags/sales_forecast_retraining.py
```python
from airflow.decorators import dag, task
from airflow.utils.dates import days_ago
from datetime import timedelta
import pandas as pd
import logging

logger = logging.getLogger(__name__)

# Default DAG arguments
default_args = {
    'owner': 'sales-forecasting',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

@dag(
    dag_id='sales_forecast_retraining',
    default_args=default_args,
    description='Automated model retraining pipeline for sales forecasting',
    schedule_interval='@daily',  # Run daily
    start_date=days_ago(1),
    catchup=False,
    tags=['ml', 'sales-forecasting', 'retraining'],
)
def sales_forecast_retraining_pipeline():
    
    @task
    def check_data_availability():
        """Check if new data is available for retraining"""
        # Implementation here
        pass
    
    @task
    def validate_data_quality():
        """Validate data quality and check for drift"""
        # Implementation here
        pass
    
    @task
    def prepare_training_data():
        """Prepare data for model training"""
        # Implementation here
        pass
    
    @task
    def train_models():
        """Train new model versions"""
        # Implementation here
        pass
    
    @task
    def evaluate_models():
        """Evaluate trained models"""
        # Implementation here
        pass
    
    @task
    def register_model():
        """Register best model in MLflow"""
        # Implementation here
        pass
    
    @task
    def send_notification():
        """Send notification about retraining results"""
        # Implementation here
        pass
    
    # Task dependencies
    data_available = check_data_availability()
    data_quality = validate_data_quality()
    training_data = prepare_training_data()
    models = train_models()
    evaluation = evaluate_models()
    registration = register_model()
    notification = send_notification()
    
    # Define workflow
    data_available >> data_quality >> training_data >> models >> evaluation >> registration >> notification

# Register the DAG
sales_forecast_retraining_dag = sales_forecast_retraining_pipeline()
```

### Data Quality Checks Implementation

#### Task: check_data_availability
```python
@task
def check_data_availability():
    """Check if new data is available for retraining"""
    from datetime import datetime, timedelta
    import os
    
    # Check if data files exist for the past day
    today = datetime.now().date()
    yesterday = today - timedelta(days=1)
    
    # Example data path
    data_path = f"/opt/airflow/include/data/sales_data_{yesterday}.parquet"
    
    if os.path.exists(data_path):
        logger.info(f"New data available for {yesterday}")
        return {"status": "available", "date": str(yesterday)}
    else:
        logger.warning(f"No new data available for {yesterday}")
        return {"status": "not_available", "date": str(yesterday)}
```

#### Task: validate_data_quality
```python
@task
def validate_data_quality(data_info: dict):
    """Validate data quality and check for drift"""
    if data_info["status"] == "not_available":
        return {"status": "skipped", "reason": "No new data"}
    
    from include.data_validation.data_validator import DataValidator
    import pandas as pd
    
    # Load data
    data_path = f"/opt/airflow/include/data/sales_data_{data_info['date']}.parquet"
    df = pd.read_parquet(data_path)
    
    # Validate data
    validator = DataValidator()
    validation_results = validator.validate_sales_data(df)
    
    # Check for drift (compare with reference data)
    reference_path = "/opt/airflow/include/data/reference_data.parquet"
    reference_df = pd.read_parquet(reference_path)
    
    drift_results = validator.check_data_drift(reference_df, df)
    
    return {
        "validation_results": validation_results,
        "drift_results": drift_results,
        "quality_score": validation_results["quality_score"]
    }
```

### Model Training Implementation

#### Task: train_models
```python
@task
def train_models(data_info: dict, quality_info: dict):
    """Train new model versions"""
    if quality_info.get("status") == "skipped":
        return {"status": "skipped", "reason": quality_info.get("reason")}
    
    # Check quality threshold
    if quality_info["quality_score"] < 0.7:
        return {"status": "failed", "reason": "Data quality below threshold"}
    
    from include.ml_models.train_models import ModelTrainer
    import pandas as pd
    import mlflow
    
    # Load data
    data_path = f"/opt/airflow/include/data/sales_data_{data_info['date']}.parquet"
    df = pd.read_parquet(data_path)
    
    # Prepare data (this would use existing feature engineering)
    # For simplicity, assuming data is already prepared
    
    # Split data
    train_size = int(len(df) * 0.7)
    val_size = int(len(df) * 0.15)
    
    train_df = df[:train_size]
    val_df = df[train_size:train_size + val_size]
    test_df = df[train_size + val_size:]
    
    # Start MLflow run
    mlflow.set_experiment("sales_forecast_retraining")
    
    with mlflow.start_run(run_name=f"retraining_{data_info['date']}") as run:
        # Train models
        trainer = ModelTrainer()
        results = trainer.train_all_models(
            train_df, val_df, test_df, 
            target_col='sales', 
            use_optuna=True
        )
        
        # Log parameters and metrics
        mlflow.log_params({
            "training_date": data_info['date'],
            "data_quality_score": quality_info["quality_score"]
        })
        
        # Log metrics for each model
        for model_name, model_results in results.items():
            if "metrics" in model_results:
                mlflow.log_metrics({
                    f"{model_name}_r2": model_results["metrics"].get("r2", 0),
                    f"{model_name}_rmse": model_results["metrics"].get("rmse", 0),
                    f"{model_name}_mae": model_results["metrics"].get("mae", 0)
                })
        
        return {
            "status": "success",
            "run_id": run.info.run_id,
            "models": list(results.keys()),
            "results": results
        }
```

### Model Evaluation and Registration

#### Task: evaluate_models
```python
@task
def evaluate_models(training_results: dict):
    """Evaluate trained models and select best one"""
    if training_results["status"] != "success":
        return {"status": "skipped", "reason": training_results.get("reason")}
    
    # Get current production model metrics for comparison
    import mlflow
    
    # Get production model run (simplified)
    try:
        production_model = mlflow.pyfunc.load_model("models:/sales_forecast_model/Production")
        # In practice, you'd compare metrics from MLflow
        production_r2 = 0.85  # Example value
    except:
        production_r2 = 0.0  # No production model yet
    
    # Compare with new models
    best_model = None
    best_r2 = production_r2
    
    for model_name, model_results in training_results["results"].items():
        model_r2 = model_results.get("metrics", {}).get("r2", 0)
        if model_r2 > best_r2:
            best_r2 = model_r2
            best_model = model_name
    
    return {
        "status": "success",
        "best_model": best_model,
        "best_r2": best_r2,
        "production_r2": production_r2,
        "improvement": best_r2 - production_r2
    }
```

#### Task: register_model
```python
@task
def register_model(evaluation_results: dict, training_results: dict):
    """Register best model in MLflow"""
    if evaluation_results["status"] != "success":
        return {"status": "skipped", "reason": evaluation_results.get("reason")}
    
    if not evaluation_results["best_model"]:
        return {"status": "skipped", "reason": "No model improvement"}
    
    import mlflow
    from mlflow.models import infer_signature
    
    # Register the best model
    model_name = evaluation_results["best_model"]
    run_id = training_results["run_id"]
    
    # Get model URI
    model_uri = f"runs:/{run_id}/{model_name}"
    
    # Register model
    model_details = mlflow.register_model(
        model_uri,
        "sales_forecast_model"
    )
    
    # Transition to production if significantly better
    if evaluation_results["improvement"] > 0.02:  # 2% improvement threshold
        mlflow_client = mlflow.MlflowClient()
        mlflow_client.transition_model_version_stage(
            name="sales_forecast_model",
            version=model_details.version,
            stage="Production",
            archive_existing_versions=True
        )
        
        return {
            "status": "success",
            "model_version": model_details.version,
            "transitioned_to_production": True,
            "improvement": evaluation_results["improvement"]
        }
    else:
        return {
            "status": "success",
            "model_version": model_details.version,
            "transitioned_to_production": False,
            "improvement": evaluation_results["improvement"]
        }
```

### Notification Implementation

#### Task: send_notification
```python
@task
def send_notification(registration_results: dict):
    """Send notification about retraining results"""
    import json
    
    if registration_results["status"] == "skipped":
        message = f"Model retraining skipped: {registration_results.get('reason')}"
    elif registration_results["status"] == "success":
        if registration_results["transitioned_to_production"]:
            message = f"New model v{registration_results['model_version']} deployed to production with {registration_results['improvement']:.2%} improvement"
        else:
            message = f"Model v{registration_results['model_version']} registered but not deployed (improvement: {registration_results['improvement']:.2%})"
    else:
        message = "Model retraining failed"
    
    # Log the message (in practice, you might send email, Slack message, etc.)
    logger.info(f"Retraining notification: {message}")
    
    return {"status": "notification_sent", "message": message}
```

## Testing Plan

### Unit Tests
- Test individual task functions
- Test data validation logic
- Test model training integration
- Test model registration logic

### Integration Tests
- Test DAG execution flow
- Test data quality checks
- Test model training and evaluation
- Test model registration

### Manual Testing
- Trigger DAG manually
- Verify task execution
- Check MLflow model registry
- Verify notifications

## Success Criteria

1. DAG is registered in Airflow
2. All tasks execute successfully
3. Models are trained with new data
4. Best models are registered in MLflow
5. Production models are updated when improved
6. Notifications are sent

## Dependencies

- Apache Airflow
- MLflow
- Existing model training code
- Data validation utilities
- Python 3.8+

## Timeline Estimate

| Task | Estimated Time |
|------|----------------|
| Create Retraining DAG Structure | 1 hour |
| Implement Data Quality Checks | 2 hours |
| Implement Model Training Logic | 3 hours |
| Implement Model Registration | 2 hours |
| Testing | 2 hours |
| **Total** | **10 hours** |