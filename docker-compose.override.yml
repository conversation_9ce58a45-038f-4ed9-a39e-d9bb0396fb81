# This file extends the Astro docker-compose setup with additional services
# It will be automatically loaded by docker-compose when running astro dev start

services:
  # MLflow tracking server
  mlflow:
    image: python:3.11-slim
    platform: linux/arm64
    command: >
      bash -c "pip install mlflow==2.9.2 psycopg2-binary boto3 && 
               export MLFLOW_S3_ENDPOINT_URL=http://minio:9000 &&
               mlflow server --host 0.0.0.0 --port 5001 
               --backend-store-uri *****************************************/mlflow 
               --default-artifact-root s3://mlflow-artifacts/ 
               --serve-artifacts"
    ports:
      - '5001:5001'
    environment:
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
      - AWS_DEFAULT_REGION=us-east-1
    depends_on:
      - mlflow-db
      - minio
    networks:
      - airflow
      - default
    restart: unless-stopped

  # PostgreSQL for MLflow
  mlflow-db:
    image: postgres:16-alpine
    platform: linux/arm64
    environment:
      POSTGRES_USER: mlflow
      POSTGRES_PASSWORD: mlflow
      POSTGRES_DB: mlflow
    volumes:
      - mlflow-db-volume:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD', 'pg_isready', '-U', 'mlflow']
      interval: 5s
      retries: 5
    networks:
      - airflow
      - default
    restart: unless-stopped

  # MinIO for S3-compatible storage
  minio:
    image: minio/minio:latest
    platform: linux/arm64
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - minio-data:/data
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9000/minio/health/live']
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - airflow
      - default
    restart: unless-stopped

  # Create MLflow bucket in MinIO
  minio-mc:
    image: minio/mc:latest
    platform: linux/arm64
    depends_on:
      - minio
    entrypoint: >
      /bin/sh -c "
      sleep 10;
      mc alias set myminio http://minio:9000 minioadmin minioadmin;
      mc mb myminio/mlflow-artifacts || true;
      mc anonymous set public myminio/mlflow-artifacts;
      exit 0;
      "

  # Add Redis to the Airflow network for Celery if needed
  redis:
    image: redis:7-alpine
    platform: linux/arm64
    expose:
      - 6379
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 5s
      timeout: 30s
      retries: 50
    restart: unless-stopped

  # Streamlit UI for model inference
  streamlit-ui:
    image: python:3.11-slim
    platform: linux/arm64
    working_dir: /app
    command: >
      bash -c "
      apt-get update && apt-get install -y libgomp1 build-essential cmake &&
      pip install --no-cache-dir streamlit==1.29.0 pandas==2.1.4 numpy==1.24.3 scikit-learn==1.3.2 matplotlib==3.8.2 seaborn==0.13.0 plotly==5.18.0 mlflow==2.9.2 boto3==1.34.14 lightgbm==4.2.0 catboost==1.2.2 xgboost==2.0.3 prometheus-client &&
      chmod +x /app/entrypoint.sh &&
      /app/entrypoint.sh
      "
    ports:
      - '8501:8501'
    environment:
      - MLFLOW_TRACKING_URI=http://mlflow:5001
      - MLFLOW_S3_ENDPOINT_URL=http://minio:9000
      - AWS_ACCESS_KEY_ID=minioadmin
      - AWS_SECRET_ACCESS_KEY=minioadmin
      - AWS_DEFAULT_REGION=us-east-1
    volumes:
      - ./ui:/app
      - ./include:/usr/local/airflow/include:ro
    depends_on:
      - mlflow
      - minio
    restart: unless-stopped

  # Prometheus monitoring
  prometheus:
    image: prom/prometheus:latest
    platform: linux/arm64
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/rules:/etc/prometheus/rules
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    restart: unless-stopped
    networks:
      - airflow
      - default

  # Grafana dashboarding
  grafana:
    image: grafana/grafana-enterprise
    platform: linux/arm64
    ports:
      - "3000:3000"
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SERVER_DOMAIN=localhost
      - GF_SERVER_ROOT_URL=%(protocol)s://%(domain)s:%(http_port)s/grafana/
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - airflow
      - default

  # ML Metrics Exporter
  ml-metrics-exporter:
    image: python:3.11-slim
    platform: linux/arm64
    working_dir: /app
    command: >
      bash -c "
      pip install --no-cache-dir prometheus-client mlflow psutil requests &&
      python -m include.monitoring.ml_metrics_exporter
      "
    ports:
      - "8080:8080"
    environment:
      - MLFLOW_TRACKING_URI=http://mlflow:5001
      - PROMETHEUS_URL=http://prometheus:9090
      - PUSHGATEWAY_URL=http://pushgateway:9091
    volumes:
      - ./include:/app/include
    networks:
      - airflow
      - default
    depends_on:
      - mlflow
      - prometheus
      - pushgateway
    restart: unless-stopped

  # Prometheus Pushgateway
  pushgateway:
    image: prom/pushgateway:latest
    platform: linux/arm64
    ports:
      - "9091:9091"
    networks:
      - airflow
      - default
    restart: unless-stopped

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    platform: linux/arm64
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - airflow
      - default
    restart: unless-stopped

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    platform: linux/arm64
    ports:
      - "8082:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    networks:
      - airflow
      - default
    restart: unless-stopped

volumes:
  mlflow-db-volume:
  minio-data:
  prometheus-data:
  grafana-storage:

networks:
  airflow:
    external: true
    name: sales-prediction_8bf96f_airflow
  default:
    name: sales-prediction_8bf96f_default