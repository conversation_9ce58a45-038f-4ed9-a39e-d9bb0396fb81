"""
Integration module for drift detection with existing ML pipeline
"""

import os
import logging
from typing import Dict, <PERSON><PERSON>, <PERSON><PERSON>, Any
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
import mlflow
from mlflow.tracking import <PERSON><PERSON><PERSON><PERSON><PERSON>

from .drift_detection import DriftDetector
from .drift_monitoring_service import DriftMonitoringService
from ..utils.mlflow_utils import MLflowManager

logger = logging.getLogger(__name__)


class DriftIntegration:
    """
    Integration class for drift detection with existing ML pipeline
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize drift integration
        
        Args:
            config_path: Path to drift configuration file
        """
        self.config_path = config_path or "/opt/airflow/include/config/drift_config.yml"
        self.mlflow_manager = MLflowManager()
        self.monitoring_service = DriftMonitoringService(config_path)
        
    def setup_drift_monitoring_for_training(self, training_data: pd.DataFrame,
                                          run_id: str) -> bool:
        """
        Set up drift monitoring after model training
        
        Args:
            training_data: Training dataset to use as reference
            run_id: MLflow run ID for the training
            
        Returns:
            True if setup successful, False otherwise
        """
        try:
            # Save training data as reference for drift detection
            reference_data_path = f"/tmp/reference_data_{run_id}.parquet"
            training_data.to_parquet(reference_data_path)
            
            # Log reference data to MLflow
            with mlflow.start_run(run_id=run_id):
                mlflow.log_artifact(reference_data_path, "reference_data")
                
                # Log drift detection metadata
                mlflow.log_params({
                    "drift_detection_enabled": True,
                    "reference_data_shape": str(training_data.shape),
                    "reference_data_timestamp": datetime.now().isoformat()
                })
            
            # Clean up temporary file
            if os.path.exists(reference_data_path):
                os.remove(reference_data_path)
            
            logger.info(f"Drift monitoring setup completed for run {run_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup drift monitoring: {e}")
            return False
    
    def check_drift_before_training(self, new_data: pd.DataFrame,
                                   reference_run_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check for drift before starting model training
        
        Args:
            new_data: New dataset to check for drift
            reference_run_id: Reference run ID (uses latest if None)
            
        Returns:
            Drift analysis results with training recommendations
        """
        try:
            # Initialize monitoring service with reference data
            if not self.monitoring_service.initialize_reference_data(reference_run_id):
                return {
                    'success': False,
                    'error': 'Failed to initialize reference data',
                    'recommendation': 'proceed_with_caution'
                }
            
            # Perform drift check
            drift_results = self.monitoring_service.check_drift(
                new_data, model_name='pre_training_check'
            )
            
            # Generate training recommendation
            recommendation = self._generate_training_recommendation(drift_results)
            
            # Add recommendation to results
            drift_results['training_recommendation'] = recommendation
            drift_results['success'] = True
            
            return drift_results
            
        except Exception as e:
            logger.error(f"Failed to check drift before training: {e}")
            return {
                'success': False,
                'error': str(e),
                'recommendation': 'proceed_with_caution'
            }
    
    def _generate_training_recommendation(self, drift_results: Dict) -> Dict[str, Any]:
        """
        Generate training recommendation based on drift analysis
        
        Args:
            drift_results: Drift analysis results
            
        Returns:
            Training recommendation dictionary
        """
        summary = drift_results.get('summary', {})
        overall_status = summary.get('overall_drift_status', 'unknown')
        
        if overall_status == 'severe':
            return {
                'action': 'retrain_required',
                'priority': 'high',
                'reason': 'Severe drift detected - model performance likely degraded',
                'suggested_actions': [
                    'Retrain model with recent data',
                    'Review feature engineering pipeline',
                    'Consider model architecture changes',
                    'Increase monitoring frequency'
                ]
            }
        elif overall_status == 'moderate':
            return {
                'action': 'retrain_recommended',
                'priority': 'medium',
                'reason': 'Moderate drift detected - proactive retraining recommended',
                'suggested_actions': [
                    'Schedule model retraining',
                    'Monitor performance closely',
                    'Consider incremental learning approaches'
                ]
            }
        elif overall_status == 'minor':
            return {
                'action': 'monitor_closely',
                'priority': 'low',
                'reason': 'Minor drift detected - continue monitoring',
                'suggested_actions': [
                    'Increase monitoring frequency',
                    'Prepare for potential retraining',
                    'Review data quality'
                ]
            }
        else:
            return {
                'action': 'continue_normal',
                'priority': 'low',
                'reason': 'No significant drift detected',
                'suggested_actions': [
                    'Continue normal operations',
                    'Maintain regular monitoring schedule'
                ]
            }
    
    def integrate_with_airflow_dag(self, task_context: Dict) -> Dict[str, Any]:
        """
        Integration function for Airflow DAG tasks
        
        Args:
            task_context: Airflow task context
            
        Returns:
            Drift check results for downstream tasks
        """
        try:
            # Extract data from task context
            data_path = task_context.get('data_path')
            if not data_path or not os.path.exists(data_path):
                raise ValueError(f"Data path not found: {data_path}")
            
            # Load data
            if data_path.endswith('.parquet'):
                data = pd.read_parquet(data_path)
            elif data_path.endswith('.csv'):
                data = pd.read_csv(data_path)
            else:
                raise ValueError(f"Unsupported file format: {data_path}")
            
            # Perform drift check
            drift_results = self.check_drift_before_training(data)
            
            # Log results to Airflow context
            task_context['drift_results'] = drift_results
            
            # Return results for XCom
            return {
                'drift_status': drift_results.get('summary', {}).get('overall_drift_status', 'unknown'),
                'recommendation': drift_results.get('training_recommendation', {}),
                'timestamp': datetime.now().isoformat(),
                'data_shape': data.shape
            }
            
        except Exception as e:
            logger.error(f"Airflow integration failed: {e}")
            return {
                'drift_status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def should_trigger_retraining(self, drift_results: Dict) -> Tuple[bool, str]:
        """
        Determine if retraining should be triggered based on drift results
        
        Args:
            drift_results: Drift analysis results
            
        Returns:
            Tuple of (should_retrain, reason)
        """
        if not drift_results.get('success', False):
            return False, "Drift analysis failed"
        
        summary = drift_results.get('summary', {})
        overall_status = summary.get('overall_drift_status', 'unknown')
        recommendation = drift_results.get('training_recommendation', {})
        
        if recommendation.get('action') == 'retrain_required':
            return True, f"Severe drift detected: {recommendation.get('reason', 'Unknown')}"
        
        if recommendation.get('action') == 'retrain_recommended':
            # Check additional criteria for moderate drift
            features_with_drift = summary.get('features_with_drift', 0)
            severe_feature_drift = summary.get('severe_feature_drift', 0)
            
            if features_with_drift >= 3 or severe_feature_drift >= 1:
                return True, f"Moderate drift with {features_with_drift} features affected"
        
        return False, f"Drift level ({overall_status}) does not require immediate retraining"
    
    def get_drift_summary_for_dashboard(self, days: int = 7) -> Dict[str, Any]:
        """
        Get drift summary for dashboard display
        
        Args:
            days: Number of days to include in summary
            
        Returns:
            Dashboard-ready drift summary
        """
        try:
            # Get drift history
            history = self.monitoring_service.get_drift_history(days)
            
            if not history:
                return {
                    'status': 'no_data',
                    'message': f'No drift data available for the last {days} days'
                }
            
            # Calculate summary statistics
            recent_checks = len(history)
            drift_detected_count = sum(1 for h in history 
                                     if h.get('summary', {}).get('overall_drift_status') != 'none')
            
            # Get latest status
            latest = history[-1] if history else {}
            latest_status = latest.get('summary', {}).get('overall_drift_status', 'unknown')
            
            # Calculate trend
            if len(history) >= 2:
                recent_drift = sum(1 for h in history[-3:] 
                                 if h.get('summary', {}).get('overall_drift_status') != 'none')
                trend = 'increasing' if recent_drift >= 2 else 'stable'
            else:
                trend = 'insufficient_data'
            
            return {
                'status': 'success',
                'summary': {
                    'period_days': days,
                    'total_checks': recent_checks,
                    'drift_detected_count': drift_detected_count,
                    'drift_detection_rate': drift_detected_count / recent_checks if recent_checks > 0 else 0,
                    'latest_status': latest_status,
                    'trend': trend,
                    'last_check': latest.get('timestamp') if latest else None
                },
                'history': history[-10:]  # Last 10 checks for detailed view
            }
            
        except Exception as e:
            logger.error(f"Failed to get drift summary: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def cleanup_old_drift_data(self, days: int = 30) -> bool:
        """
        Clean up old drift monitoring data
        
        Args:
            days: Number of days to retain
            
        Returns:
            True if cleanup successful
        """
        try:
            # This would typically clean up stored drift data, logs, etc.
            # For now, just log the action
            logger.info(f"Cleaning up drift data older than {days} days")
            
            # Clean up monitoring service history
            cutoff_date = datetime.now() - timedelta(days=days)
            original_count = len(self.monitoring_service.drift_history)
            
            self.monitoring_service.drift_history = [
                result for result in self.monitoring_service.drift_history
                if datetime.fromisoformat(result['timestamp']) > cutoff_date
            ]
            
            cleaned_count = original_count - len(self.monitoring_service.drift_history)
            logger.info(f"Cleaned up {cleaned_count} old drift records")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to cleanup drift data: {e}")
            return False

    def should_enable_automated_retraining(self, training_results: Dict, data_quality_score: float) -> bool:
        """
        Determine if automated retraining should be enabled based on training results
        """
        try:
            # Check if training was successful
            if not training_results or not training_results.get('models_trained'):
                logger.warning("Training results incomplete, disabling automated retraining")
                return False

            # Check data quality
            if data_quality_score < 0.7:
                logger.warning(f"Data quality too low ({data_quality_score:.2f}), disabling automated retraining")
                return False

            # Check if we have good model performance
            best_model_metrics = None
            for model_name, model_data in training_results.items():
                if isinstance(model_data, dict) and 'metrics' in model_data:
                    metrics = model_data['metrics']
                    if metrics.get('r2', 0) > 0.7:  # Good R2 score
                        best_model_metrics = metrics
                        break

            if not best_model_metrics:
                logger.warning("No models with good performance found, disabling automated retraining")
                return False

            # Check configuration
            retraining_config = self.config.get('retraining', {})
            if not retraining_config.get('enabled', True):
                logger.info("Automated retraining disabled in configuration")
                return False

            logger.info("Automated retraining enabled - all conditions met")
            return True

        except Exception as e:
            logger.error(f"Failed to check automated retraining conditions: {e}")
            return False

    def trigger_retraining_dag(self, reason: str, context: Dict) -> bool:
        """
        Trigger the automated retraining DAG
        """
        try:
            from airflow.api.client.local_client import Client

            # This would trigger the retraining DAG in a real Airflow environment
            # For now, we'll just log the trigger
            logger.info(f"RETRAINING TRIGGER: {reason}")
            logger.info(f"Context: {context}")

            # In production, you would use:
            # client = Client(None, None)
            # client.trigger_dag(dag_id='sales_forecast_retraining', conf=context)

            return True

        except Exception as e:
            logger.error(f"Failed to trigger retraining DAG: {e}")
            return False
