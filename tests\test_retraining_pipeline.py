"""
Test suite for automated retraining pipeline
"""

import pytest
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# Add include path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'include'))

from ml_models.model_versioning import ModelVersionManager
from ml_models.model_validation import ModelValidator
from ml_models.drift_integration import DriftIntegration


class TestModelVersionManager:
    """Test model versioning functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.version_manager = ModelVersionManager()
    
    @patch('mlflow.tracking.MlflowClient')
    def test_get_model_versions(self, mock_client):
        """Test getting model versions"""
        # Mock MLflow client response
        mock_version = Mock()
        mock_version.version = "1"
        mock_version.current_stage = "Production"
        mock_version.run_id = "test_run_id"
        mock_version.creation_timestamp = 1640995200000  # 2022-01-01
        mock_version.last_updated_timestamp = 1640995200000
        
        mock_run = Mock()
        mock_run.data.metrics = {"rmse": 0.1, "r2": 0.9}
        mock_run.data.params = {"n_estimators": 100}
        mock_run.data.tags = {"model_type": "random_forest"}
        
        mock_client.return_value.search_model_versions.return_value = [mock_version]
        mock_client.return_value.get_run.return_value = mock_run
        
        # Test
        versions = self.version_manager.get_model_versions("test_model")
        
        assert len(versions) == 1
        assert versions[0]['version'] == "1"
        assert versions[0]['stage'] == "Production"
        assert versions[0]['metrics']['rmse'] == 0.1
    
    def test_compare_model_versions(self):
        """Test model version comparison"""
        # Mock version data
        with patch.object(self.version_manager, 'get_model_versions') as mock_get_versions:
            mock_get_versions.return_value = [
                {
                    'version': '1',
                    'metrics': {'rmse': 0.2, 'r2': 0.8},
                    'params': {'n_estimators': 100}
                },
                {
                    'version': '2', 
                    'metrics': {'rmse': 0.15, 'r2': 0.85},
                    'params': {'n_estimators': 200}
                }
            ]
            
            comparison = self.version_manager.compare_model_versions("test_model", "1", "2")
            
            assert 'metrics_comparison' in comparison
            assert comparison['metrics_comparison']['rmse']['better_version'] == '2'
            assert comparison['metrics_comparison']['r2']['better_version'] == '2'


class TestModelValidator:
    """Test model validation functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.validator = ModelValidator()
        
        # Create sample data
        np.random.seed(42)
        self.X_test = pd.DataFrame({
            'feature1': np.random.randn(1000),
            'feature2': np.random.randn(1000),
            'feature3': np.random.randn(1000)
        })
        self.y_test = pd.Series(np.random.randn(1000) * 10 + 100)  # Sales-like data
    
    def test_validate_model_performance(self):
        """Test model performance validation"""
        # Mock model
        mock_model = Mock()
        mock_model.predict.return_value = self.y_test + np.random.randn(len(self.y_test)) * 0.1
        
        # Test validation
        validation_results = self.validator.validate_model_performance(
            mock_model, self.X_test, self.y_test
        )
        
        assert 'performance_metrics' in validation_results
        assert 'threshold_checks' in validation_results
        assert 'overall_status' in validation_results
        assert validation_results['performance_metrics']['prediction_count'] == 1000
    
    def test_validate_business_rules(self):
        """Test business rule validation"""
        # Create predictions with some negative values
        y_pred = np.concatenate([
            np.random.randn(990) * 10 + 100,  # Normal predictions
            np.array([-5, -10, -15, -20, -25])  # Some negative predictions
        ])
        
        business_validation = self.validator._validate_business_rules(self.y_test, y_pred)
        
        assert 'negative_predictions' in business_validation
        assert business_validation['negative_predictions']['count'] == 5
        assert business_validation['negative_predictions']['ratio'] == 0.005
    
    def test_model_stability_validation(self):
        """Test model stability validation"""
        # Mock stable model
        mock_model = Mock()
        mock_model.predict.return_value = self.y_test + np.random.randn(len(self.y_test)) * 0.01  # Very stable
        
        stability_results = self.validator.validate_model_stability(
            mock_model, self.X_test, self.y_test
        )
        
        assert 'metrics_stability' in stability_results
        assert 'overall_stability' in stability_results
        assert stability_results['bootstrap_samples'] > 0


class TestDriftIntegration:
    """Test drift integration functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.drift_integration = DriftIntegration()
        
        # Create sample data
        np.random.seed(42)
        self.sample_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100),
            'store_id': np.random.choice(['store_1', 'store_2', 'store_3'], 100),
            'sales': np.random.randn(100) * 100 + 1000,
            'quantity_sold': np.random.randint(1, 100, 100),
            'profit': np.random.randn(100) * 50 + 200
        })
    
    def test_check_drift_before_training(self):
        """Test drift checking before training"""
        drift_results = self.drift_integration.check_drift_before_training(self.sample_data)
        
        assert 'summary' in drift_results
        assert 'feature_drift' in drift_results
        assert 'timestamp' in drift_results
    
    def test_should_trigger_retraining(self):
        """Test retraining trigger logic"""
        # Test with high drift
        high_drift_results = {
            'summary': {
                'overall_drift_status': 'severe',
                'features_with_drift': 5,
                'severe_feature_drift': 2
            }
        }
        
        should_retrain, reason = self.drift_integration.should_trigger_retraining(high_drift_results)
        assert should_retrain == True
        assert 'severe' in reason.lower()
        
        # Test with low drift
        low_drift_results = {
            'summary': {
                'overall_drift_status': 'stable',
                'features_with_drift': 0,
                'severe_feature_drift': 0
            }
        }
        
        should_retrain, reason = self.drift_integration.should_trigger_retraining(low_drift_results)
        assert should_retrain == False
    
    def test_should_enable_automated_retraining(self):
        """Test automated retraining enablement logic"""
        # Good training results
        good_results = {
            'models_trained': ['random_forest', 'xgboost'],
            'random_forest': {
                'metrics': {'r2': 0.85, 'rmse': 0.1}
            }
        }
        
        should_enable = self.drift_integration.should_enable_automated_retraining(
            good_results, data_quality_score=0.9
        )
        assert should_enable == True
        
        # Poor training results
        poor_results = {
            'models_trained': ['random_forest'],
            'random_forest': {
                'metrics': {'r2': 0.5, 'rmse': 0.5}
            }
        }
        
        should_enable = self.drift_integration.should_enable_automated_retraining(
            poor_results, data_quality_score=0.9
        )
        assert should_enable == False


class TestRetrainingPipelineIntegration:
    """Integration tests for the complete retraining pipeline"""
    
    def setup_method(self):
        """Setup test environment"""
        self.drift_integration = DriftIntegration()
        self.model_validator = ModelValidator()
        self.version_manager = ModelVersionManager()
    
    def test_end_to_end_retraining_decision(self):
        """Test end-to-end retraining decision process"""
        # Create sample data
        np.random.seed(42)
        sample_data = pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=1000),
            'store_id': np.random.choice(['store_1', 'store_2'], 1000),
            'sales': np.random.randn(1000) * 100 + 1000,
            'quantity_sold': np.random.randint(1, 100, 1000),
            'profit': np.random.randn(1000) * 50 + 200
        })
        
        # Step 1: Check drift
        drift_results = self.drift_integration.check_drift_before_training(sample_data)
        should_retrain, reason = self.drift_integration.should_trigger_retraining(drift_results)
        
        # Step 2: If retraining triggered, validate the process
        if should_retrain:
            # Mock training results
            training_results = {
                'models_trained': ['random_forest'],
                'random_forest': {
                    'metrics': {'r2': 0.8, 'rmse': 0.15, 'mae': 0.1}
                }
            }
            
            # Check if automated retraining should be enabled
            auto_retrain_enabled = self.drift_integration.should_enable_automated_retraining(
                training_results, data_quality_score=0.85
            )
            
            assert isinstance(auto_retrain_enabled, bool)
        
        # Verify the process completed without errors
        assert isinstance(should_retrain, bool)
        assert isinstance(reason, str)
    
    @patch('mlflow.tracking.MlflowClient')
    def test_model_promotion_workflow(self, mock_client):
        """Test model promotion workflow"""
        # Mock successful validation
        mock_model = Mock()
        mock_model.predict.return_value = np.random.randn(100) * 10 + 100
        
        X_test = pd.DataFrame(np.random.randn(100, 3))
        y_test = pd.Series(np.random.randn(100) * 10 + 100)
        
        validation_results = self.model_validator.validate_model_performance(
            mock_model, X_test, y_test
        )
        
        # Check if validation passed
        if validation_results['overall_status'] in ['excellent', 'good', 'acceptable']:
            # Mock promotion
            mock_client.return_value.transition_model_version_stage.return_value = None
            
            promotion_result = self.version_manager.promote_model_to_production(
                "test_model", "1"
            )
            
            assert promotion_result['status'] == 'success'


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
