"""
Prometheus metrics for Streamlit UI
"""

import time
import logging
from typing import Dict, Any, Optional
from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry, generate_latest, CONTENT_TYPE_LATEST
import streamlit as st

logger = logging.getLogger(__name__)

# Create a custom registry for UI metrics
UI_METRICS_REGISTRY = CollectorRegistry()

# UI-specific metrics
ui_page_views = Counter(
    'streamlit_page_views_total',
    'Total number of page views',
    ['page_name'],
    registry=UI_METRICS_REGISTRY
)

ui_prediction_requests = Counter(
    'streamlit_prediction_requests_total',
    'Total number of prediction requests',
    ['model_type', 'status'],
    registry=UI_METRICS_REGISTRY
)

ui_prediction_duration = Histogram(
    'streamlit_prediction_duration_seconds',
    'Time spent generating predictions',
    ['model_type'],
    registry=UI_METRICS_REGISTRY
)

ui_model_load_duration = Histogram(
    'streamlit_model_load_duration_seconds',
    'Time spent loading models',
    registry=UI_METRICS_REGISTRY
)

ui_data_upload_size = Histogram(
    'streamlit_data_upload_size_bytes',
    'Size of uploaded data files',
    registry=UI_METRICS_REGISTRY
)

ui_active_sessions = Gauge(
    'streamlit_active_sessions',
    'Number of active Streamlit sessions',
    registry=UI_METRICS_REGISTRY
)

ui_model_info = Info(
    'streamlit_loaded_model_info',
    'Information about currently loaded models',
    registry=UI_METRICS_REGISTRY
)

ui_error_count = Counter(
    'streamlit_errors_total',
    'Total number of errors',
    ['error_type'],
    registry=UI_METRICS_REGISTRY
)


class PrometheusMetrics:
    """
    Prometheus metrics collector for Streamlit UI
    """
    
    def __init__(self):
        self.start_time = time.time()
        self.session_id = self._get_session_id()
        
    def _get_session_id(self) -> str:
        """Get current Streamlit session ID"""
        try:
            # Try to get session ID from Streamlit
            if hasattr(st, 'session_state') and hasattr(st.session_state, 'session_id'):
                return st.session_state.session_id
            else:
                # Fallback to a simple timestamp-based ID
                return f"session_{int(time.time())}"
        except:
            return f"session_{int(time.time())}"
    
    def track_page_view(self, page_name: str) -> None:
        """Track page view"""
        try:
            ui_page_views.labels(page_name=page_name).inc()
        except Exception as e:
            logger.warning(f"Failed to track page view: {e}")
    
    def track_prediction_request(self, model_type: str, status: str = 'success') -> None:
        """Track prediction request"""
        try:
            ui_prediction_requests.labels(model_type=model_type, status=status).inc()
        except Exception as e:
            logger.warning(f"Failed to track prediction request: {e}")
    
    def track_prediction_duration(self, model_type: str, duration: float) -> None:
        """Track prediction duration"""
        try:
            ui_prediction_duration.labels(model_type=model_type).observe(duration)
        except Exception as e:
            logger.warning(f"Failed to track prediction duration: {e}")
    
    def track_model_load_duration(self, duration: float) -> None:
        """Track model loading duration"""
        try:
            ui_model_load_duration.observe(duration)
        except Exception as e:
            logger.warning(f"Failed to track model load duration: {e}")
    
    def track_data_upload(self, file_size: int) -> None:
        """Track data upload"""
        try:
            ui_data_upload_size.observe(file_size)
        except Exception as e:
            logger.warning(f"Failed to track data upload: {e}")
    
    def update_active_sessions(self, count: int) -> None:
        """Update active sessions count"""
        try:
            ui_active_sessions.set(count)
        except Exception as e:
            logger.warning(f"Failed to update active sessions: {e}")
    
    def update_model_info(self, model_info: Dict[str, str]) -> None:
        """Update model information"""
        try:
            ui_model_info.info(model_info)
        except Exception as e:
            logger.warning(f"Failed to update model info: {e}")
    
    def track_error(self, error_type: str) -> None:
        """Track error occurrence"""
        try:
            ui_error_count.labels(error_type=error_type).inc()
        except Exception as e:
            logger.warning(f"Failed to track error: {e}")
    
    def get_metrics(self) -> str:
        """Get metrics in Prometheus format"""
        try:
            return generate_latest(UI_METRICS_REGISTRY)
        except Exception as e:
            logger.error(f"Failed to generate metrics: {e}")
            return ""
    
    def get_metrics_dict(self) -> Dict[str, Any]:
        """Get metrics as dictionary for display"""
        try:
            metrics = {}
            
            # Get current metric values (this is a simplified version)
            # In a real implementation, you'd parse the metrics from the registry
            metrics['page_views'] = 'Available in /metrics endpoint'
            metrics['prediction_requests'] = 'Available in /metrics endpoint'
            metrics['active_sessions'] = 'Available in /metrics endpoint'
            metrics['uptime_seconds'] = time.time() - self.start_time
            
            return metrics
        except Exception as e:
            logger.error(f"Failed to get metrics dict: {e}")
            return {}


# Global metrics instance
metrics = PrometheusMetrics()


def track_function_call(func_name: str):
    """Decorator to track function calls"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Track based on function name
                if 'predict' in func_name.lower():
                    model_type = kwargs.get('model_type', 'unknown')
                    metrics.track_prediction_request(model_type, 'success')
                    metrics.track_prediction_duration(model_type, duration)
                elif 'load' in func_name.lower() and 'model' in func_name.lower():
                    metrics.track_model_load_duration(duration)
                
                return result
            except Exception as e:
                # Track error
                metrics.track_error(func_name)
                raise e
        return wrapper
    return decorator


def create_metrics_endpoint():
    """Create a simple metrics endpoint for Streamlit"""
    try:
        # This would typically be served by a separate HTTP server
        # For Streamlit, we can display metrics in the UI
        metrics_data = metrics.get_metrics()
        return metrics_data
    except Exception as e:
        logger.error(f"Failed to create metrics endpoint: {e}")
        return ""


def display_metrics_in_sidebar():
    """Display metrics in Streamlit sidebar"""
    try:
        with st.sidebar:
            st.markdown("---")
            st.subheader("📊 Metrics")
            
            metrics_dict = metrics.get_metrics_dict()
            
            if metrics_dict:
                st.metric("Uptime", f"{metrics_dict.get('uptime_seconds', 0):.0f}s")
                
                # Add a button to show detailed metrics
                if st.button("View Detailed Metrics"):
                    st.code(metrics.get_metrics(), language='text')
            else:
                st.info("Metrics not available")
                
    except Exception as e:
        logger.warning(f"Failed to display metrics in sidebar: {e}")


def initialize_ui_metrics():
    """Initialize UI metrics tracking"""
    try:
        # Track initial page view
        metrics.track_page_view("main")
        
        # Update session info
        metrics.update_active_sessions(1)  # Simplified - would need session management
        
        logger.info("UI metrics initialized")
    except Exception as e:
        logger.warning(f"Failed to initialize UI metrics: {e}")


# Export the metrics instance and key functions
__all__ = [
    'metrics',
    'track_function_call',
    'create_metrics_endpoint',
    'display_metrics_in_sidebar',
    'initialize_ui_metrics',
    'UI_METRICS_REGISTRY'
]
