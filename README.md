# Sales Forecasting Pipeline

## Project Overview

This is a comprehensive sales forecasting system built on Apache Airflow using the Astronomer platform. The project implements a complete machine learning pipeline for predicting sales data with features including data generation, validation, model training, evaluation, and deployment.

The system uses multiple ML algorithms (XGBoost, LightGBM, and Facebook Prophet) to create ensemble models for accurate sales predictions. It integrates with MLflow for experiment tracking and model registry, and MinIO for artifact storage.

## Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                          User Interface                             │
│                        (Streamlit App)                              │
└─────────────────────┬───────────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────────┐
│                            Airflow                                  │
│  ┌────────────────────────────────────────────────────────────────┐ │
│  │                    Sales Forecast DAG                          │ │
│  │  ┌─────────────┐  ┌──────────────┐  ┌──────────────────────┐   │ │
│  │  │  Extract    │  │   Validate   │  │      Train           │   │ │
│  │  │   Data      │─▶│    Data      │─▶│     Models           │   │ │
│  │  └─────────────┘  └──────────────┘  └──────────────────────┘   │ │
│  │  ┌────────────────────────────────────────────────────────────┐ │ │
│  │  │                         Evaluate & Register                │ │ │
│  │  │  ┌──────────────┐  ┌────────────────┐  ┌─────────────────┐ │ │ │
│  │  │  │  Evaluate    │  │   Register     │  │  Transition to  │ │ │ │
│  │  │  │   Models     │─▶│    Models      │─▶│   Production    │ │ │ │
│  │  │  └──────────────┘  └────────────────┘  └─────────────────┘ │ │ │
│  │  └────────────────────────────────────────────────────────────┘ │ │
│  └────────────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────────┐
│                           ML Services                               │
│  ┌────────────────────────────────────────────────────────────────┐ │
│  │                          MLflow                                │ │
│  │  • Experiment Tracking                                         │ │
│  │  • Model Registry                                              │ │
│  │  • Model Serving                                               │ │
│  └────────────────────────────────────────────────────────────────┘ │
│  ┌────────────────────────────────────────────────────────────────┐ │
│  │                          MinIO                                 │ │
│  │  • Artifact Storage                                            │ │
│  │  • Model Persistence                                           │ │
│  └────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

## System Design

### Data Pipeline
1. **Data Generation**: Creates realistic sales data with multiple stores, products, and business patterns
2. **Data Validation**: Ensures data quality and consistency before processing
3. **Feature Engineering**: Extracts temporal and business features for model training
4. **Model Training**: Trains multiple ML models (XGBoost, LightGBM, Prophet)
5. **Model Evaluation**: Compares model performance and selects the best model
6. **Model Registration**: Registers models in MLflow model registry
7. **Production Deployment**: Transitions best models to production stage

### Key Components
- **DAGs**: Orchestrate the end-to-end ML pipeline
- **ML Models**: XGBoost, LightGBM, and Prophet for ensemble predictions
- **MLflow**: For experiment tracking, model registry, and model serving
- **MinIO**: S3-compatible storage for artifacts and model persistence
- **Streamlit UI**: Interactive interface for model inference and visualization

## Technology Stack

### Core Technologies
- **Apache Airflow**: Workflow orchestration
- **Astronomer CLI**: Local development and deployment
- **Docker**: Containerization
- **Python**: Primary programming language

### Machine Learning Libraries
- **XGBoost**: Gradient boosting framework
- **LightGBM**: Light gradient boosting machine
- **Facebook Prophet**: Time series forecasting
- **Scikit-learn**: Machine learning utilities
- **Optuna**: Hyperparameter optimization

### Data Processing
- **Pandas**: Data manipulation and analysis
- **NumPy**: Numerical computing
- **PyArrow**: Parquet file handling

### Model Management
- **MLflow**: Experiment tracking and model registry
- **MinIO**: S3-compatible object storage
- **PostgreSQL**: Metadata storage for MLflow

### Visualization & UI
- **Streamlit**: Interactive web application
- **Plotly**: Interactive charts and graphs
- **Matplotlib/Seaborn**: Static visualizations

## Prerequisites

### System Requirements
- Docker Desktop (Windows/Mac) or Docker Engine (Linux)
- Python 3.8 or higher
- At least 8GB RAM (16GB recommended)
- 10GB free disk space

### Software Dependencies
- Astronomer CLI (`astro`)
- Git (for version control)

## Installation

### 1. Install Astronomer CLI
Follow the official installation guide: https://www.astronomer.io/docs/astro/cli/install-cli



### 2. Clone the Repository
```bash
git clone <repository-url>
cd Sales_prediction
```

### 3. Initialize the Project
```bash
astro dev init
```

### 4. Install Python Dependencies
The project will automatically install dependencies from `requirements.txt` when starting the environment.

## Getting Started

### 1. Start the Development Environment
```bash
astro dev start
```

This command will start all necessary services:
- Airflow Webserver (http://localhost:8080)
- Airflow Scheduler
- PostgreSQL Database (localhost:5432)
- MLflow Tracking Server (http://localhost:5001)
- MinIO Storage (http://localhost:9000)
- Streamlit UI (http://localhost:8501)

### 2. Access Airflow UI
1. Open your browser and navigate to http://localhost:8080
2. Login with default credentials:
   - Username: `admin`
   - Password: `admin`

### 3. Run the Sales Forecast Training Pipeline
1. In the Airflow UI, locate the `sales_forecast_training` DAG
2. Toggle the DAG to "On" state
3. Click the "Trigger DAG" button to start the pipeline
4. Monitor the progress in the Graph View

### 4. Access MLflow UI
1. Open your browser and navigate to http://localhost:5001
2. View experiments, model metrics, and registered models

### 5. Access MinIO UI
1. Open your browser and navigate to http://localhost:9001
2. Login with credentials:
   - Username: `minioadmin`
   - Password: `minioadmin`

### 6. Use the Streamlit Inference UI
1. Open your browser and navigate to http://localhost:8501
2. Load trained models and make sales predictions



## Project Structure

```
.
├── dags/                  # Airflow DAG definitions
│   └── sales_forecast_training.py
├── include/               # Project modules and utilities
│   ├── config/            # Configuration files
│   ├── data_validation/   # Data validation utilities
│   ├── feature_engineering/ # Feature engineering pipeline
│   ├── ml_models/         # Machine learning models
│   └── utils/             # Utility functions
├── ui/                    # Streamlit user interface
├── tests/                 # Unit and integration tests
├── Dockerfile             # Astro Runtime configuration
├── requirements.txt       # Python dependencies
└── docker-compose.override.yml # Additional services
```

## Usage

### Training New Models
1. Ensure all services are running with `astro dev start`
2. Trigger the `sales_forecast_training` DAG in Airflow UI
3. Monitor training progress in MLflow UI
4. View model performance metrics and visualizations

### Making Predictions
1. Access the Streamlit UI at http://localhost:8501
2. Load the latest trained models
3. Upload historical sales data or enter manual data
4. Generate sales forecasts for future periods

### Model Management
1. View all experiments in MLflow UI at http://localhost:5001
2. Compare model performance across different runs
3. Register best models to model registry
4. Transition models to production stage

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   - Stop existing Docker containers or change ports in `docker-compose.override1.yml`

2. **Insufficient Resources**
   - Ensure Docker has allocated sufficient memory (8GB+)

3. **Model Training Failures**
   - Check MLflow logs for detailed error information
   - Verify data quality in validation steps

4. **UI Access Issues**
   - Ensure all services are running with `astro dev ps`
   - Restart services with `astro dev restart`

### Useful Commands

```bash
# Start development environment
astro dev start

# Stop development environment
astro dev stop

# View running services
astro dev ps

# Restart services
astro dev restart

# Access Airflow CLI
astro dev bash

# Run tests
astro dev pytest
```

## Planned Enhancements

This project has comprehensive plans for additional features that enhance its production readiness:

### Monitoring & Observability
- **Prometheus Integration**: Complete metrics collection for ML pipeline monitoring
- **Grafana Dashboarding**: Interactive dashboards for visualizing model performance and system health

### Model Management
- **Automated Retraining**: Scheduled model retraining pipeline with Airflow
- **Drift Detection**: Automated detection of feature, prediction, and concept drift

### DevOps & Deployment
- **CI/CD Pipeline**: GitHub Actions workflows for automated testing and deployment
- **FastAPI Backend**: RESTful API with HTML/CSS frontend for model serving

All enhancement plans are fully documented with implementation guides. See the [FINAL_IMPLEMENTATION_SUMMARY.md](FINAL_IMPLEMENTATION_SUMMARY.md) for complete details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests to ensure nothing is broken
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For support or questions, please open an issue in the repository.