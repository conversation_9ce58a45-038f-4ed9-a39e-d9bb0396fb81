# Prometheus Monitoring Integration

## Overview

This document describes the comprehensive Prometheus monitoring integration implemented for the Sales Prediction ML Pipeline. The integration provides real-time monitoring, alerting, and observability for all ML pipeline components.

## Architecture

### Components

1. **Prometheus Server** - Central metrics collection and storage
2. **ML Metrics Exporter** - Custom service for ML-specific metrics
3. **Pushgateway** - For batch job metrics
4. **Node Exporter** - System-level metrics
5. **cAdvisor** - Container metrics
6. **Alerting Rules** - Automated alert generation
7. **Grafana Integration** - Dashboard and visualization support

### Metrics Categories

#### 1. Model Performance Metrics
- `model_predictions_total` - Total number of predictions
- `model_prediction_duration_seconds` - Prediction latency
- `model_prediction_errors_total` - Prediction errors
- `model_accuracy_score` - Model accuracy metrics

#### 2. Training Pipeline Metrics
- `training_duration_seconds` - Training job duration
- `training_jobs_total` - Training job counts by status
- `model_versions_total` - Model version counts by stage

#### 3. Drift Detection Metrics
- `feature_drift_detected` - Feature drift detection status
- `concept_drift_detected` - Concept drift detection status
- `prediction_drift_detected` - Prediction drift detection status
- `psi_drift_score` - Population Stability Index scores
- `drift_alerts_triggered_total` - Drift alert counts

#### 4. Data Quality Metrics
- `data_quality_score` - Data quality scores by dimension
- `data_missing_values_total` - Missing value counts
- `data_last_update_timestamp` - Data freshness timestamps

#### 5. System Resource Metrics
- `ml_pipeline_memory_usage_bytes` - Memory usage
- `ml_pipeline_cpu_usage_percent` - CPU usage
- `pipeline_health_score` - Overall pipeline health
- `service_availability` - Service availability status

## Configuration

### Prometheus Configuration (`prometheus/prometheus.yml`)

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'sales-prediction-ml'
    environment: 'production'

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'ml-metrics-exporter'
    static_configs:
      - targets: ['ml-metrics-exporter:8080']
    scrape_interval: 60s
  
  - job_name: 'drift-detection'
    static_configs:
      - targets: ['drift-monitoring:8000']
    scrape_interval: 60s
```

### Alerting Rules (`prometheus/rules/ml_pipeline_alerts.yml`)

Key alert rules include:
- **HighFeatureDrift** - Feature drift > 0.3
- **SevereFeatureDrift** - Feature drift > 0.5
- **ConceptDriftDetected** - Concept drift > 0.2
- **ModelPerformanceDegradation** - Increasing prediction errors
- **HighPredictionLatency** - P95 latency > 1.0s
- **TrainingJobFailed** - Training DAG failures
- **DataQualityIssues** - Data quality score < 0.7

## Services

### ML Metrics Exporter

**Location**: `include/monitoring/ml_metrics_exporter.py`

**Features**:
- Automatic MLflow integration
- Real-time metrics collection
- Background metric updates
- Prometheus-compatible endpoint

**Usage**:
```python
from monitoring.ml_metrics_exporter import start_metrics_server

# Start the metrics server
start_metrics_server(port=8080, update_interval=60)

# Record predictions
exporter = get_metrics_exporter()
exporter.record_prediction("sales_model", "1.0", duration=0.1)
```

### Prometheus Integration Service

**Location**: `include/monitoring/prometheus_integration.py`

**Features**:
- Query Prometheus metrics
- Calculate pipeline health scores
- Export data for Grafana
- Active alert monitoring

**Usage**:
```python
from monitoring.prometheus_integration import get_prometheus_integration

integration = get_prometheus_integration()
health = integration.get_ml_pipeline_health()
print(f"Pipeline health: {health['health_status']}")
```

## Docker Services

### Additional Services in `docker-compose.override.yml`

```yaml
services:
  ml-metrics-exporter:
    image: python:3.11-slim
    ports:
      - "8080:8080"
    environment:
      - MLFLOW_TRACKING_URI=http://mlflow:5001
      - PROMETHEUS_URL=http://prometheus:9090

  pushgateway:
    image: prom/pushgateway:latest
    ports:
      - "9091:9091"

  node-exporter:
    image: prom/node-exporter:latest
    ports:
      - "9100:9100"

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    ports:
      - "8082:8080"
```

## Streamlit UI Integration

The Streamlit UI now includes a monitoring dashboard accessible through the main interface:

**Features**:
- Real-time pipeline health display
- Service status indicators
- Active alerts summary
- Health score visualization

**Access**: Click "🔍 View Monitoring Dashboard" in the Streamlit UI

## Configuration Management

### Monitoring Configuration (`include/config/monitoring_config.yml`)

Comprehensive configuration covering:
- Prometheus settings
- Alert thresholds
- Health check endpoints
- Dashboard configuration
- Security settings
- Integration settings

### Key Configuration Sections

```yaml
alerting:
  thresholds:
    prediction_latency_p95_ms: 500
    feature_drift_warning: 0.3
    feature_drift_critical: 0.5
    model_accuracy_min: 0.8

health_checks:
  services:
    mlflow:
      url: "http://mlflow:5001/health"
      timeout: 10
      expected_status: 200
```

## Testing

### Test Suite (`tests/test_prometheus_monitoring.py`)

Comprehensive tests covering:
- ML metrics exporter functionality
- Prometheus integration
- Health score calculation
- Alert generation
- Metric recording

**Run Tests**:
```bash
python -m pytest tests/test_prometheus_monitoring.py -v
```

## Deployment

### Starting the Monitoring Stack

1. **Start all services**:
   ```bash
   astro dev start
   ```

2. **Access monitoring interfaces**:
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3000 (admin/admin)
   - ML Metrics: http://localhost:8080/metrics
   - Pushgateway: http://localhost:9091

### Verification

1. **Check Prometheus targets**: Navigate to Status > Targets in Prometheus UI
2. **Verify metrics collection**: Query metrics like `model_predictions_total`
3. **Test alerting**: Trigger alerts by simulating drift or errors
4. **Monitor health**: Check pipeline health in Streamlit UI

## Metrics Endpoints

- **ML Metrics Exporter**: `http://ml-metrics-exporter:8080/metrics`
- **Drift Detection**: `http://drift-monitoring:8000/metrics`
- **Streamlit UI**: `http://streamlit-ui:8501/metrics`
- **Node Exporter**: `http://node-exporter:9100/metrics`
- **cAdvisor**: `http://cadvisor:8080/metrics`

## Health Scoring

The system calculates an overall pipeline health score based on:

### Service Availability (40% weight)
- MLflow: 15%
- Airflow Webserver: 10%
- Airflow Scheduler: 10%
- Streamlit UI: 5%

### Performance Metrics (60% weight)
- Prediction Latency: 15%
- Prediction Error Rate: 15%
- Training Success Rate: 15%
- Drift Detection Rate: 15%

### Health Status Categories
- **Excellent**: 90-100%
- **Good**: 80-89%
- **Warning**: 60-79%
- **Critical**: <60%

## Troubleshooting

### Common Issues

1. **Metrics not appearing**:
   - Check service connectivity
   - Verify scrape configurations
   - Check firewall/network settings

2. **High memory usage**:
   - Adjust retention settings
   - Optimize query frequency
   - Check for metric cardinality issues

3. **Alert fatigue**:
   - Adjust alert thresholds
   - Implement cooldown periods
   - Review alert routing

### Debugging Commands

```bash
# Check Prometheus configuration
curl http://localhost:9090/api/v1/status/config

# List all metrics
curl http://localhost:9090/api/v1/label/__name__/values

# Check specific metric
curl "http://localhost:9090/api/v1/query?query=model_predictions_total"

# View active alerts
curl http://localhost:9090/api/v1/alerts
```

## Future Enhancements

1. **Advanced Analytics**:
   - Anomaly detection
   - Predictive alerting
   - Trend analysis

2. **Integration Expansions**:
   - Slack/Teams notifications
   - PagerDuty integration
   - Email alerting

3. **Dashboard Improvements**:
   - Custom Grafana dashboards
   - Business metrics visualization
   - Real-time streaming

## Security Considerations

- Enable authentication in production
- Configure TLS/SSL for secure communication
- Implement proper access controls
- Regular security updates

## Maintenance

- Regular backup of Prometheus data
- Monitor disk usage and retention
- Update alert rules as needed
- Review and optimize queries
- Performance tuning based on usage patterns
