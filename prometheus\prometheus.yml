# Prometheus configuration file for Sales Prediction ML Pipeline
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'sales-prediction-ml'
    environment: 'production'

# Rule files for alerting
rule_files:
  - "rules/*.yml"

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Scrape Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Scrape FastAPI application
  - job_name: 'fastapi'
    static_configs:
      - targets: ['fastapi:8000']
    scrape_interval: 15s
    metrics_path: /metrics

  # Scrape Streamlit UI with custom metrics
  - job_name: 'streamlit-ui'
    static_configs:
      - targets: ['streamlit-ui:8501']
    scrape_interval: 30s
    metrics_path: /metrics

  # Scrape MLflow
  - job_name: 'mlflow'
    static_configs:
      - targets: ['mlflow:5001']
    scrape_interval: 30s
    metrics_path: /metrics

  # Scrape Airflow services
  - job_name: 'airflow-webserver'
    static_configs:
      - targets: ['airflow-webserver:8080']
    scrape_interval: 30s
    metrics_path: /admin/metrics

  - job_name: 'airflow-scheduler'
    static_configs:
      - targets: ['airflow-scheduler:8080']
    scrape_interval: 30s
    metrics_path: /admin/metrics

  # Scrape ML Model Drift Detection Service
  - job_name: 'drift-detection'
    static_configs:
      - targets: ['drift-monitoring:8000']
    scrape_interval: 60s
    metrics_path: /metrics
    scrape_timeout: 30s

  # Scrape Pushgateway for batch job metrics
  - job_name: 'pushgateway'
    static_configs:
      - targets: ['pushgateway:9091']
    scrape_interval: 15s
    honor_labels: true

  # Scrape Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Scrape cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Scrape PostgreSQL metrics (for Airflow metadata)
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Scrape Redis metrics (if used for caching)
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Custom ML Pipeline Metrics
  - job_name: 'ml-pipeline-metrics'
    static_configs:
      - targets: ['ml-metrics-exporter:8080']
    scrape_interval: 60s
    metrics_path: /metrics
    scrape_timeout: 30s