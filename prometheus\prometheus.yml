# Prometheus configuration file
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  # Scrape Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Scrape FastAPI application
  - job_name: 'fastapi'
    static_configs:
      - targets: ['fastapi:8000']

  # Scrape Streamlit UI
  - job_name: 'streamlit-ui'
    static_configs:
      - targets: ['streamlit-ui:8501']

  # Scrape MLflow
  - job_name: 'mlflow'
    static_configs:
      - targets: ['mlflow:5001']

  # Scrape Airflow services
  - job_name: 'airflow-webserver'
    static_configs:
      - targets: ['airflow-webserver:8080']

  - job_name: 'airflow-scheduler'
    static_configs:
      - targets: ['airflow-scheduler:8080']